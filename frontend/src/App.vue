<template>
  <div id="app" class="search-engine-layout">
    <!-- 简洁导航栏 -->
    <nav v-if="showNavbar" class="navbar">
      <div class="navbar-content">
        <router-link to="/" class="navbar-brand">
          🔍 AI搜索
        </router-link>
        
        <div class="navbar-nav">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link v-if="authStore.isAuthenticated" to="/dashboard" class="nav-link">我的智能体</router-link>
          <router-link v-if="authStore.isAdmin" to="/admin" class="nav-link">管理</router-link>
        </div>
        
        <div class="navbar-user">
          <template v-if="authStore.isAuthenticated">
            <router-link to="/profile" class="user-avatar" :title="authStore.user?.email">
              {{ authStore.user?.email?.[0]?.toUpperCase() }}
            </router-link>
            <button @click="logout" class="btn btn-ghost">退出</button>
          </template>
          <template v-else>
            <router-link to="/login" class="btn btn-ghost">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </template>
        </div>
      </div>
    </nav>

    <!-- 加载状态 -->
    <div v-if="!authStore.initialized" class="app-loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在初始化...</div>
      </div>
    </div>

    <!-- 全局消息 -->
    <div v-if="globalMessage.show" :class="['global-message', globalMessage.type]">
      {{ globalMessage.text }}
    </div>

    <!-- 主要内容 -->
    <router-view v-if="authStore.initialized" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 全局消息系统
const globalMessage = ref({
  show: false,
  text: '',
  type: 'success' as 'success' | 'error' | 'warning'
})

// 显示导航栏的条件（简化）
const showNavbar = computed(() => {
  const hideNavRoutes = ['/login', '/register']
  return authStore.initialized && !hideNavRoutes.includes(route.path)
})

// 全局消息显示函数
const showMessage = (text: string, type: 'success' | 'error' | 'warning' = 'success') => {
  globalMessage.value = { show: true, text, type }
  setTimeout(() => {
    globalMessage.value.show = false
  }, 3000)
}

// 退出登录
const logout = () => {
  authStore.logout()
  showMessage('已安全退出', 'success')
}

// 应用初始化
onMounted(async () => {
  console.log('App组件已挂载，开始初始化...')
  
  // 监听认证过期事件
  const handleAuthExpired = () => {
    console.log('收到认证过期事件，清除认证状态并跳转到登录页')
    authStore.logout()
    if (route.path !== '/login' && route.path !== '/register') {
      router.push('/login')
    }
  }
  
  window.addEventListener('auth-expired', handleAuthExpired)
  
  try {
    await authStore.initialize()
    await nextTick()
    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
    showMessage('应用初始化失败', 'error')
  }
})

// 在组件卸载时清除事件监听器
onUnmounted(() => {
  window.removeEventListener('auth-expired', () => {})
})

// 全局消息函数注入到window
declare global {
  interface Window {
    showMessage: (text: string, type?: 'success' | 'error' | 'warning') => void
    debugAuth: () => void
  }
}

// 全局函数
window.showMessage = showMessage

// 开发环境调试函数
if ((import.meta as any).env.DEV) {
  window.debugAuth = () => {
    console.log('=== 认证状态调试信息 ===')
    console.log('已初始化:', authStore.initialized)
    console.log('已认证:', authStore.isAuthenticated)
    console.log('用户信息:', authStore.user)
    console.log('Token存在:', !!authStore.token)
    console.log('Token值:', authStore.token ? authStore.token.substring(0, 50) + '...' : 'null')
    console.log('localStorage token:', localStorage.getItem('token') ? 'EXISTS' : 'NOT_FOUND')
    console.log('当前路由:', route.path)
    console.log('========================')
    
    if (authStore.isAuthenticated) {
      console.log('尝试刷新认证状态...')
      authStore.refreshAuthStatus().then(success => {
        console.log('刷新结果:', success ? '成功' : '失败')
      })
    }
  }
}
</script>

<style scoped>
.search-engine-layout {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 应用加载状态 */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: #5f6368;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  font-weight: 400;
  color: #5f6368;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 全局消息通知 */
.global-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  z-index: 10000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 320px;
  animation: slideInRight 0.3s ease-out;
}

.global-message.success {
  background: #1e8e3e;
}

.global-message.error {
  background: #d93025;
}

.global-message.warning {
  background: #f57c00;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 导航栏样式 */
.navbar {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #202124;
  font-size: 20px;
  font-weight: 400;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  text-decoration: none;
  color: #5f6368;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  background: #f8f9fa;
  color: #1a73e8;
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #1a73e8;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  background: #1557b0;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.btn-ghost {
  background: #ffffff;
  color: #5f6368;
  border: 1px solid #dadce0;
}

.btn-ghost:hover {
  background: #f8f9fa;
  color: #d93025;
}

.btn-primary {
  background: #1a73e8;
  color: #ffffff;
}

.btn-primary:hover {
  background: #1557b0;
}

/* 主要内容区域 */
.main-content {
  min-height: calc(100vh - 64px);
  flex: 1;
}

.main-content.with-navbar {
  padding-top: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-content {
    padding: 12px 16px;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .navbar-nav {
    order: 2;
    width: 100%;
    justify-content: center;
    gap: 16px;
  }
  
  .navbar-user {
    order: 1;
  }
  
  .navbar-brand {
    font-size: 18px;
  }
  
  .nav-link {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .navbar-content {
    padding: 8px 12px;
  }
  
  .navbar-brand {
    font-size: 16px;
  }
  
  .btn {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}
</style> 