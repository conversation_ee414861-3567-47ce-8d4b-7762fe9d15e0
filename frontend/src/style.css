/* ========== 全局重置和基础样式 ========== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #202124;
  background: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ========== 搜索引擎风格的布局 ========== */
.search-engine-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* ========== 简洁的导航栏 ========== */
.navbar {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.navbar-brand {
  font-size: 22px;
  font-weight: 500;
  color: #1a73e8;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.navbar-brand:hover {
  color: #1557b0;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
  padding: 8px 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.nav-link:hover, .nav-link.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1a73e8;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.user-avatar:hover {
  background: #1557b0;
}

/* ========== 搜索引擎风格的主页 ========== */
.search-home {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  min-height: calc(100vh - 64px);
}

.search-logo {
  font-size: 90px;
  font-weight: 400;
  color: #1a73e8;
  margin-bottom: 32px;
  text-align: center;
  letter-spacing: -2px;
}

.search-container {
  width: 100%;
  max-width: 584px;
  margin-bottom: 32px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 44px;
  border: 1px solid #dfe1e5;
  border-radius: 24px;
  padding: 0 52px 0 16px;
  font-size: 16px;
  outline: none;
  transition: box-shadow 0.2s ease, border-color 0.2s ease;
}

.search-input:hover {
  box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
  border-color: rgba(223,225,229,0);
}

.search-input:focus {
  box-shadow: 0 2px 8px 1px rgba(64,60,67,.24);
  border-color: rgba(223,225,229,0);
}

.search-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9aa0a6;
  transition: color 0.2s ease;
}

.search-btn:hover {
  color: #1a73e8;
}

/* ========== AI智能体网格 ========== */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 24px;
}

.agent-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.agent-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #dadce0;
  transform: translateY(-2px);
}

.agent-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.agent-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.agent-description {
  font-size: 14px;
  color: #5f6368;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 12px;
  color: #70757a;
}

/* ========== 简洁的按钮样式 ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 24px;
  height: 36px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
}

.btn-primary {
  background: #1a73e8;
  color: #ffffff;
  border-color: #1a73e8;
}

.btn-primary:hover {
  background: #1557b0;
  border-color: #1557b0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn-secondary {
  background: #ffffff;
  color: #3c4043;
  border-color: #dadce0;
}

.btn-secondary:hover {
  background: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn-ghost {
  color: #1a73e8;
}

.btn-ghost:hover {
  background: rgba(26, 115, 232, 0.04);
}

/* ========== 表单样式 ========== */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* ========== 消息提示 ========== */
.global-message {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.global-message.success {
  background: #e8f5e8;
  color: #1e8e3e;
  border: 1px solid #81c995;
}

.global-message.error {
  background: #fce8e6;
  color: #d93025;
  border: 1px solid #f28b82;
}

.global-message.warning {
  background: #fef7e0;
  color: #ea8600;
  border: 1px solid #fbbc04;
}

/* ========== 加载状态 ========== */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  color: #5f6368;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 16px;
    height: 56px;
  }
  
  .navbar-nav {
    gap: 16px;
  }
  
  .search-logo {
    font-size: 60px;
    margin-bottom: 24px;
  }
  
  .search-container {
    max-width: 90%;
  }
  
  .agents-grid {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }
  
  .search-home {
    padding: 0 16px;
    min-height: calc(100vh - 56px);
  }
}

@media (max-width: 480px) {
  .navbar-brand {
    font-size: 18px;
  }
  
  .search-logo {
    font-size: 48px;
  }
  
  .nav-link {
    font-size: 13px;
  }
} 