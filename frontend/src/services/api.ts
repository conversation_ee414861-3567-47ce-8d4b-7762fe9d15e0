import axios, { AxiosInstance, AxiosResponse } from 'axios'
import type { 
  LoginRequest, RegisterRequest,
  User, Agent, Transaction, BaseModel, Conversation, Message,
  CreateAgentRequest, CreateConversationRequest, SendMessageRequest,
  PaginatedResponse, ApiResponse, CreateModelRequest, UpdateModelRequest,
  UsersResponse
} from '../types'

// 获取API基础URL
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if ((import.meta as any).env.VITE_API_BASE_URL) {
    return (import.meta as any).env.VITE_API_BASE_URL
  }
  
  // 如果在开发环境且没有设置环境变量，尝试自动检测
  if ((import.meta as any).env.DEV) {
    const hostname = window.location.hostname
    return `http://${hostname}:8080/api/v1`
  }
  
  // 生产环境默认使用相对路径
  return '/api/v1'
}

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 30000,
})

// 添加调试信息
console.log('API Base URL:', getApiBaseUrl())

// 请求拦截器 - 添加token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
    console.log('已添加Authorization头部')
  } else {
    console.log('未找到token，跳过添加Authorization头部')
  }
  
  // 调试信息
  console.log('发送请求:', {
    method: config.method?.toUpperCase(),
    url: config.url,
    fullURL: (config.baseURL || '') + (config.url || ''),
    headers: config.headers
  })
  
  return config
}, (error) => {
  console.error('请求拦截器错误:', error)
  return Promise.reject(error)
})

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    
    // 如果是401错误且不是登录或注册请求
    if (error.response?.status === 401) {
      const isAuthRequest = originalRequest.url?.includes('/auth/login') || 
                           originalRequest.url?.includes('/auth/register')
      
      if (!isAuthRequest) {
        console.log('API请求401错误，token可能已过期，清除本地存储')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        
        // 触发全局消息提示
        if (window.showMessage) {
          window.showMessage('登录已过期，请重新登录', 'warning')
        }
        
        // 使用事件通知认证状态变化，让App组件处理路由跳转
        window.dispatchEvent(new CustomEvent('auth-expired'))
      }
    }
    
    return Promise.reject(error)
  }
)

// 认证API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<{ token: string; user: User }> => {
    const response: AxiosResponse<{ token: string; user: User }> = await api.post('/auth/login', credentials)
    return response.data
  },

  register: async (userData: RegisterRequest): Promise<{ token: string; user: User }> => {
    const response: AxiosResponse<{ token: string; user: User }> = await api.post('/auth/register', userData)
    return response.data
  },

  refreshToken: async (): Promise<{ token: string }> => {
    const response: AxiosResponse<{ token: string }> = await api.post('/auth/refresh')
    return response.data
  },

  getProfile: async (): Promise<User> => {
    const response: AxiosResponse<User> = await api.get('/user/profile')
    return response.data
  },

  updateProfile: async (profileData: Partial<User>): Promise<void> => {
    await api.put('/user/profile', profileData)
  },

  getTransactions: async (page = 1, limit = 20): Promise<PaginatedResponse<Transaction>> => {
    const response: AxiosResponse<PaginatedResponse<Transaction>> = await api.get('/user/transactions', {
      params: { page, limit }
    })
    return response.data
  },

  getConversations: async (): Promise<Conversation[]> => {
    const response: AxiosResponse<Conversation[]> = await api.get('/user/conversations')
    return response.data
  }
}

// 智能体API
export const agentApi = {
  getPublicAgents: async (): Promise<Agent[]> => {
    const response: AxiosResponse<Agent[]> = await api.get('/public/agents')
    return response.data
  },

  getAgent: async (id: number): Promise<Agent> => {
    const response: AxiosResponse<Agent> = await api.get(`/public/agents/${id}`)
    return response.data
  },

  getUserAgents: async (): Promise<Agent[]> => {
    const response: AxiosResponse<Agent[]> = await api.get('/agents')
    return response.data
  },

  createAgent: async (agentData: CreateAgentRequest): Promise<Agent> => {
    const response: AxiosResponse<Agent> = await api.post('/agents', agentData)
    return response.data
  },

  updateAgent: async (id: number, agentData: CreateAgentRequest): Promise<Agent> => {
    const response: AxiosResponse<Agent> = await api.put(`/agents/${id}`, agentData)
    return response.data
  },

  deleteAgent: async (id: number): Promise<void> => {
    await api.delete(`/agents/${id}`)
  },

  publishAgent: async (id: number): Promise<void> => {
    await api.post(`/agents/${id}/publish`)
  },

  getModels: async (): Promise<BaseModel[]> => {
    const response: AxiosResponse<BaseModel[]> = await api.get('/public/models')
    return response.data
  }
}

// 聊天API
export const chatApi = {
  createConversation: async (conversationData: CreateConversationRequest): Promise<Conversation> => {
        const response: AxiosResponse<Conversation> = await api.post('/chat/conversations', conversationData)
    return response.data
  },

  getConversation: async (id: number): Promise<Conversation> => {
        const response: AxiosResponse<Conversation> = await api.get(`/chat/conversations/${id}`)
    return response.data
  },

  sendMessage: async (conversationId: number, messageData: SendMessageRequest): Promise<{
    userMessage: Message
    botMessage: Message
    tokensUsed: number
  }> => {
        const response = await api.post(`/chat/conversations/${conversationId}/messages`, messageData)
    return response.data
  },

  getMessages: async (conversationId: number): Promise<Message[]> => {
        const response: AxiosResponse<Message[]> = await api.get(`/chat/conversations/${conversationId}/messages`)
    return response.data
  },

  clearMessages: async (conversationId: number): Promise<void> => {
        await api.delete(`/chat/conversations/${conversationId}/messages`)
  },

  deleteConversation: async (id: number): Promise<void> => {
        await api.delete(`/chat/conversations/${id}`)
  }
}

// 管理员API
export const adminApi = {
  // 用户管理
  getUsers: async (page = 1, limit = 20): Promise<UsersResponse> => {
    const response: AxiosResponse<UsersResponse> = await api.get(`/admin/users?page=${page}&limit=${limit}`)
    return response.data
  },

  banUser: async (userId: number): Promise<void> => {
    await api.put(`/admin/users/${userId}/ban`)
  },

  unbanUser: async (userId: number): Promise<void> => {
    await api.put(`/admin/users/${userId}/unban`)
  },

  // 智能体管理  
  getAllAgents: async (): Promise<Agent[]> => {
    const response: AxiosResponse<Agent[]> = await api.get('/admin/agents')
    return response.data
  },

  getPendingAgents: async (): Promise<Agent[]> => {
    const response: AxiosResponse<Agent[]> = await api.get('/admin/agents/pending')
    return response.data
  },

  approveAgent: async (agentId: number, note?: string): Promise<void> => {
    await api.put(`/admin/agents/${agentId}/approve`, { note })
  },

  rejectAgent: async (agentId: number, reason: string): Promise<void> => {
    await api.put(`/admin/agents/${agentId}/reject`, { reason })
  },

  // 交易管理
  getAllTransactions: async (page = 1, limit = 50): Promise<PaginatedResponse<Transaction>> => {
    const response: AxiosResponse<PaginatedResponse<Transaction>> = await api.get(`/admin/transactions?page=${page}&limit=${limit}`)
    return response.data
  },

  // 模型管理
  getAllModels: async (): Promise<{ models: BaseModel[], total: number }> => {
    const response: AxiosResponse<{ models: BaseModel[], total: number }> = await api.get('/admin/models')
    return response.data
  },

  createModel: async (modelData: CreateModelRequest): Promise<{ message: string, model: BaseModel }> => {
    const response: AxiosResponse<{ message: string, model: BaseModel }> = await api.post('/admin/models', modelData)
    return response.data
  },

  updateModel: async (modelId: number, modelData: Partial<CreateModelRequest>): Promise<{ message: string, model: BaseModel }> => {
    const response: AxiosResponse<{ message: string, model: BaseModel }> = await api.put(`/admin/models/${modelId}`, modelData)
    return response.data
  },

  deleteModel: async (modelId: number): Promise<{ message: string }> => {
    const response: AxiosResponse<{ message: string }> = await api.delete(`/admin/models/${modelId}`)
    return response.data
  }
}

// 测试函数 - 仅用于调试
export const testConversationsAPI = async () => {
  console.log('=== 测试 Conversations API ===')
  console.log('当前 baseURL:', getApiBaseUrl())
  console.log('localStorage token:', localStorage.getItem('token') ? 'EXISTS' : 'MISSING')
  
  try {
        const response = await api.post('/chat/conversations', {
      agentId: 1
    })
    console.log('API调用成功:', response.data)
    return response.data
  } catch (error: any) {
    console.error('API调用失败:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      config: {
        method: error.config?.method,
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        fullURL: (error.config?.baseURL || '') + (error.config?.url || '')
      }
    })
    throw error
  }
}

// 在开发环境中暴露测试函数到全局
if ((import.meta as any).env.DEV) {
  (window as any).testConversationsAPI = testConversationsAPI
}

export default api 