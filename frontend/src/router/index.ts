import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由懒加载
const Login = () => import('@/views/Login.vue')
const Register = () => import('@/views/Register.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const Marketplace = () => import('@/views/Marketplace.vue')
const AgentEditor = () => import('@/views/AgentEditor.vue')
const Chat = () => import('@/views/Chat.vue')
const Profile = () => import('@/views/Profile.vue')
const Admin = () => import('@/views/Admin.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Marketplace
    // 不需要认证，任何人都可以访问首页
  },
  {
    path: '/marketplace',
    redirect: '/'
    // 重定向到首页
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/agent-editor/:id?',
    name: 'AgentEditor',
    component: AgentEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/chat/:id?',
    name: 'Chat',
    component: Chat,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
    meta: { requiresAuth: true, requiresAdmin: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 等待认证状态初始化完成
  if (!authStore.initialized) {
    console.log('等待认证状态初始化...')
    await authStore.initialize()
  }
  
  console.log(`路由导航: ${from.path} -> ${to.path}`)
  console.log(`认证状态: ${authStore.isAuthenticated ? '已登录' : '未登录'}`)
  console.log(`管理员状态: ${authStore.isAdmin ? '是' : '否'}`)
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    console.log('需要认证但未登录，跳转到登录页')
    next('/login')
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    console.log('已登录用户访问访客页面，跳转到首页')
    next('/')
  } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
    console.log('需要管理员权限但不是管理员，跳转到首页')
    next('/')
  } else {
    console.log('路由检查通过，允许访问')
    next()
  }
})

export default router 