// 图标映射
const iconMap: Record<string, string> = {
  bot: '🤖',
  assistant: '🧠',
  code: '💻',
  write: '✍️',
  creative: '🎨',
  business: '💼',
  education: '📚',
  travel: '✈️'
}

// 获取智能体图标
export const getAgentIcon = (icon: string): string => {
  return iconMap[icon] || '🤖'
}

// 获取用户角色文本
export const getRoleText = (role?: string): string => {
  const roleMap: Record<string, string> = {
    'admin': '管理员',
    'user': '用户'
  }
  return roleMap[role || 'user'] || '用户'
}

// 获取智能体状态文本
export const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'pending_review': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
} 