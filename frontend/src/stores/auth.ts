import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest, RegisterRequest } from '@/types'
import { authApi } from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const loading = ref(false)
  const initialized = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.email === '<EMAIL>' || user.value?.role === 'admin')

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      token.value = response.token
      user.value = response.user
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      console.log('登录成功，用户信息已保存')
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    try {
      const response = await authApi.register(userData)
      token.value = response.token
      user.value = response.user
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      console.log('注册成功，用户信息已保存')
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    console.log('执行登出操作')
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    initialized.value = false
  }

  // 获取用户信息
  const fetchProfile = async () => {
    if (!token.value) {
      console.log('无token，跳过获取用户信息')
      return
    }
    
    try {
      console.log('开始获取用户信息...')
      const profile = await authApi.getProfile()
      user.value = profile
      localStorage.setItem('user', JSON.stringify(profile))
      console.log('用户信息获取成功:', profile.email)
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      // 如果是401错误说明token过期，自动登出
      if (error.response?.status === 401) {
        console.log('Token过期，执行自动登出')
        logout()
      }
      throw error
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      await authApi.updateProfile(profileData)
      if (user.value) {
        Object.assign(user.value, profileData)
        localStorage.setItem('user', JSON.stringify(user.value))
      }
    } catch (error) {
      throw error
    }
  }

  // 初始化认证状态
  const initialize = async () => {
    if (initialized.value) {
      console.log('认证状态已初始化，跳过')
      return
    }

    console.log('开始初始化认证状态...')
    
    try {
      // 从localStorage恢复token
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')
      
      if (storedToken) {
        console.log('发现存储的token，尝试恢复状态')
        token.value = storedToken
        
        // 尝试恢复用户信息
        if (storedUser) {
          try {
            user.value = JSON.parse(storedUser)
            console.log('用户信息从缓存恢复成功:', user.value?.email)
          } catch (e) {
            console.warn('用户信息缓存解析失败')
            localStorage.removeItem('user')
          }
        }
        
        // 验证token有效性并获取最新用户信息
        try {
          await fetchProfile()
          console.log('Token验证成功，认证状态恢复完成')
        } catch (error: any) {
          console.log('Token验证失败，清除认证状态:', error.message)
          logout()
        }
      } else {
        console.log('未发现存储的token')
      }
    } catch (error) {
      console.error('初始化认证状态时出错:', error)
      logout()
    } finally {
      initialized.value = true
      console.log('认证状态初始化完成，当前状态:', isAuthenticated.value ? '已登录' : '未登录')
    }
  }

  // 检查token有效性
  const validateToken = async () => {
    if (!token.value) return false
    
    try {
      await fetchProfile()
      return true
    } catch (error) {
      return false
    }
  }

  // 手动刷新认证状态
  const refreshAuthStatus = async () => {
    console.log('手动刷新认证状态...')
    if (!token.value) {
      console.log('没有token，无法刷新')
      return false
    }
    
    try {
      await fetchProfile()
      console.log('认证状态刷新成功')
      return true
    } catch (error: any) {
      console.log('认证状态刷新失败:', error.message)
      if (error.response?.status === 401) {
        logout()
      }
      return false
    }
  }

  return {
    user,
    token,
    loading,
    initialized,
    isAuthenticated,
    isAdmin,
    login,
    register,
    logout,
    fetchProfile,
    updateProfile,
    initialize,
    validateToken,
    refreshAuthStatus
  }
}) 