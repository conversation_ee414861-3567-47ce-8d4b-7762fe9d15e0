// 简化的类型定义
export interface User {
  id: number
  email: string
  nickname?: string
  contactInfo?: string
  tokenBalance: number
  role?: string
  affiliateCode?: string
  referredBy?: string
  isBanned: boolean
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  nickname?: string
}

export interface Agent {
  id: number
  name: string
  description: string
  icon: string
  creatorId: number
  isPublic: boolean
  modelId: number
  profitMargin: number
  usageCount: number
  status: string
  isCustom: boolean
  createdAt: string
  updatedAt: string
  persona: string
  useGoogleSearch: boolean
  knowledge: string
  affiliateEnabled: boolean
  affiliateCommissionRate: number
  creator?: User
  model?: BaseModel
}

// 基础模型接口
export interface BaseModel {
  id: number
  name: string
  url: string
  apiKey: string
  modelName: string
  inputCost: number
  outputCost: number
  contextWindow?: number
  createdAt: string
  updatedAt: string
}

export interface Message {
  id: number
  conversationId: number
  role: string
  content: string
  error?: string
  createdAt: string
}

export interface Conversation {
  id: number
  userId: number
  agentId: number
  title: string
  createdAt: string
  updatedAt: string
  user?: User
  agent?: Agent
  messages?: Message[]
}

export interface Transaction {
  id: number
  userId: number
  agentId: number
  type: string
  tokenAmount: number
  description: string
  createdAt: string
  updatedAt: string
  user?: User
  agent?: Agent
}

// API相关类型
export interface CreateAgentRequest {
  name: string
  description: string
  icon: string
  modelId: number
  profitMargin: number
  persona: string
  useGoogleSearch: boolean
  knowledge: string
  isPublic: boolean
  affiliateEnabled: boolean
  affiliateCommissionRate: number
}

export interface CreateConversationRequest {
  agentId: number
  title?: string
}

export interface SendMessageRequest {
  content: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
}

// 用户管理API的专门响应类型
export interface UsersResponse {
  users: User[]
  total: number
  page: number
  limit: number
}

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

// 创建模型请求接口
export interface CreateModelRequest {
  name: string
  url: string
  apiKey: string
  modelName: string
  inputCost: number
  outputCost: number
  contextWindow?: number
}

// 更新模型请求接口
export interface UpdateModelRequest {
  name?: string
  url?: string
  apiKey?: string
  modelName?: string
  inputCost?: number
  outputCost?: number
  contextWindow?: number
} 