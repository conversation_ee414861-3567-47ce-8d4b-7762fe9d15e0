<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <header class="profile-header">
      <div class="container">
        <div class="header-content">
          <div class="user-info">
            <div class="user-avatar">{{ userInitial }}</div>
            <div class="user-details">
              <h1 class="user-name">{{ user?.nickname || user?.email?.split('@')[0] || '用户' }}</h1>
              <p class="user-email">{{ user?.email }}</p>
            </div>
          </div>
          
          <div class="header-actions">
            <router-link to="/dashboard" class="back-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              返回控制台
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="profile-main">
      <div class="container">
        <!-- 导航标签 -->
        <nav class="tab-nav">
          <button 
            v-for="tab in tabs" 
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="['tab-btn', { active: activeTab === tab.id }]"
          >
            {{ tab.name }}
          </button>
        </nav>

        <!-- 标签内容 -->
        <div class="tab-content">
          <!-- 账户信息 -->
          <div v-if="activeTab === 'account'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">账户信息</h2>
              <p class="panel-subtitle">管理您的基本信息</p>
            </div>

            <form @submit.prevent="updateProfile" class="profile-form">
              <div class="form-group">
                <label for="nickname" class="form-label">昵称</label>
                <input
                  id="nickname"
                  v-model="profileForm.nickname"
                  type="text"
                  class="form-input"
                  placeholder="请输入昵称"
                  required
                  :disabled="updating"
                />
              </div>

              <div class="form-group">
                <label for="email" class="form-label">邮箱地址</label>
                <input
                  id="email"
                  v-model="profileForm.email"
                  type="email"
                  class="form-input"
                  placeholder="请输入邮箱地址"
                  required
                  :disabled="updating"
                />
              </div>

              <div class="form-actions">
                <button type="submit" class="save-btn" :disabled="updating">
                  <span v-if="!updating">保存更改</span>
                  <div v-else class="loading-spinner"></div>
                </button>
              </div>
            </form>
          </div>

          <!-- 安全设置 -->
          <div v-if="activeTab === 'security'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">安全设置</h2>
              <p class="panel-subtitle">保护您的账户安全</p>
            </div>

            <form @submit.prevent="changePassword" class="security-form">
              <div class="form-group">
                <label for="currentPassword" class="form-label">当前密码</label>
                <input
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  type="password"
                  class="form-input"
                  placeholder="请输入当前密码"
                  required
                  :disabled="changingPassword"
                />
              </div>

              <div class="form-group">
                <label for="newPassword" class="form-label">新密码</label>
                <input
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  type="password"
                  class="form-input"
                  placeholder="请输入新密码（至少6位）"
                  required
                  minlength="6"
                  :disabled="changingPassword"
                />
              </div>

              <div class="form-group">
                <label for="confirmPassword" class="form-label">确认密码</label>
                <input
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  class="form-input"
                  placeholder="请再次输入新密码"
                  required
                  :disabled="changingPassword"
                />
                <div v-if="passwordMismatch" class="field-error">密码不匹配</div>
              </div>

              <div class="form-actions">
                <button type="submit" class="change-password-btn" :disabled="!canChangePassword || changingPassword">
                  <span v-if="!changingPassword">修改密码</span>
                  <div v-else class="loading-spinner"></div>
                </button>
              </div>
            </form>
          </div>

          <!-- 交易记录 -->
          <div v-if="activeTab === 'transactions'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">交易记录</h2>
              <p class="panel-subtitle">查看您的消费历史</p>
            </div>

            <div v-if="transactions.length === 0" class="empty-state">
              <div class="empty-icon">💳</div>
              <h3 class="empty-title">暂无交易记录</h3>
              <p class="empty-subtitle">您还没有任何消费记录</p>
            </div>

            <div v-else class="transactions-list">
              <div v-for="transaction in transactions" :key="transaction.id" class="transaction-item">
                <div class="transaction-info">
                  <div class="transaction-title">{{ transaction.title }}</div>
                  <div class="transaction-time">{{ formatDate(transaction.createdAt) }}</div>
                </div>
                <div class="transaction-amount">¥{{ transaction.amount }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'

const authStore = useAuthStore()

// 当前用户
const user = computed(() => authStore.user)

// 用户头像字母
const userInitial = computed(() => {
  const name = user.value?.nickname || user.value?.email?.split('@')[0] || '用户'
  return name.charAt(0).toUpperCase()
})

// 标签管理
const activeTab = ref('account')
const tabs = [
  { id: 'account', name: '账户信息' },
  { id: 'security', name: '安全设置' },
  { id: 'transactions', name: '交易记录' }
]

// 表单状态
const updating = ref(false)
const changingPassword = ref(false)

// 个人资料表单
const profileForm = ref({
  nickname: '',
  email: ''
})

// 密码修改表单
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 交易记录
const transactions = ref<any[]>([])

// 密码匹配检查
const passwordMismatch = computed(() => {
  return passwordForm.value.confirmPassword && 
         passwordForm.value.newPassword !== passwordForm.value.confirmPassword
})

// 是否可以修改密码
const canChangePassword = computed(() => {
  return passwordForm.value.currentPassword.length >= 6 &&
         passwordForm.value.newPassword.length >= 6 &&
         passwordForm.value.newPassword === passwordForm.value.confirmPassword &&
         !changingPassword.value
})

// 初始化表单数据
const initializeForm = () => {
  if (user.value) {
    profileForm.value.nickname = user.value.nickname || ''
    profileForm.value.email = user.value.email || ''
  }
}

// 更新个人资料
const updateProfile = async () => {
  if (updating.value) return

  updating.value = true
  try {
    await api.put('/user/profile', {
      nickname: profileForm.value.nickname,
      email: profileForm.value.email
    })
    
    // 更新本地用户信息
    await authStore.fetchProfile()
    
    window.showMessage?.('个人资料更新成功', 'success')
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '更新失败，请重试', 'error')
  } finally {
    updating.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!canChangePassword.value) return

  changingPassword.value = true
  try {
    await api.post('/user/change-password', {
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword
    })
    
    // 清空表单
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    
    window.showMessage?.('密码修改成功', 'success')
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '密码修改失败，请重试', 'error')
  } finally {
    changingPassword.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载交易记录
const loadTransactions = async () => {
  try {
    const response = await api.get('/user/transactions')
    transactions.value = response.data || []
  } catch (error) {
    console.error('加载交易记录失败:', error)
  }
}

onMounted(() => {
  initializeForm()
  loadTransactions()
})
</script>

<style scoped>
.profile-page {
  background: #ffffff;
  min-height: calc(100vh - 64px);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面头部 */
.profile-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: #1a73e8;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 24px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 4px 0;
}

.user-email {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  color: #1a73e8;
  background: #f8f9fa;
}

/* 主要内容 */
.profile-main {
  padding: 32px 0;
}

/* 导航标签 */
.tab-nav {
  display: flex;
  border-bottom: 1px solid #e8eaed;
  margin-bottom: 32px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 0;
  margin-right: 32px;
  font-size: 14px;
  color: #5f6368;
  cursor: pointer;
  position: relative;
  transition: color 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: #1a73e8;
}

.tab-btn.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

/* 标签内容 */
.tab-content {
  max-width: 600px;
}

.tab-panel {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

.panel-header {
  margin-bottom: 32px;
}

.panel-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.panel-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
}

/* 表单样式 */
.profile-form,
.security-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
}

.form-input {
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.form-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-input:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.field-error {
  color: #d93025;
  font-size: 12px;
}

.form-actions {
  padding-top: 16px;
}

.save-btn,
.change-password-btn {
  height: 44px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  font-family: inherit;
}

.save-btn:hover:not(:disabled),
.change-password-btn:hover:not(:disabled) {
  background: #1557b0;
}

.save-btn:disabled,
.change-password-btn:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.empty-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
}

/* 交易记录 */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: #e8eaed;
  border-radius: 8px;
  overflow: hidden;
}

.transaction-item {
  background: #ffffff;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
}

.transaction-time {
  font-size: 12px;
  color: #5f6368;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 500;
  color: #1a73e8;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-name {
    font-size: 20px;
  }

  .tab-nav {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-nav::-webkit-scrollbar {
    display: none;
  }

  .tab-btn {
    white-space: nowrap;
    margin-right: 24px;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .transaction-amount {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .user-info {
    gap: 12px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .form-actions {
    padding-top: 8px;
  }
}
</style> 