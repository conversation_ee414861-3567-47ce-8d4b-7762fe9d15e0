<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <header class="dashboard-header">
      <div class="container">
        <div class="header-content">
          <div class="header-title-section">
            <h1 class="page-title">我的智能体</h1>
            <p class="page-subtitle">管理和创建您的AI助手</p>
          </div>
          
          <div class="header-actions">
            <router-link to="/agent-editor" class="create-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
              </svg>
              创建智能体
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 统计概览 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">🤖</div>
            <div class="stat-content">
              <div class="stat-number">{{ agents.length }}</div>
              <div class="stat-label">我的智能体</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">💬</div>
            <div class="stat-content">
              <div class="stat-number">{{ totalUsage }}</div>
              <div class="stat-label">总对话数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{{ approvedAgents }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">⏱️</div>
            <div class="stat-content">
              <div class="stat-number">{{ pendingAgents }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 智能体管理 -->
    <section class="agents-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">智能体列表</h2>
          <div class="section-actions">
            <div class="search-container">
              <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                class="search-input"
                placeholder="搜索智能体..."
              />
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载智能体...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="filteredAgents.length === 0" class="empty-state">
          <div v-if="agents.length === 0" class="empty-content">
            <div class="empty-icon">🤖</div>
            <h3 class="empty-title">创建您的第一个智能体</h3>
            <p class="empty-subtitle">开始构建专属于您的AI助手</p>
            <router-link to="/agent-editor" class="empty-action-btn">立即创建</router-link>
          </div>
          
          <div v-else class="empty-content">
            <div class="empty-icon">🔍</div>
            <h3 class="empty-title">未找到匹配的智能体</h3>
            <p class="empty-subtitle">尝试使用其他关键词搜索</p>
          </div>
        </div>

        <!-- 智能体列表 -->
        <div v-else class="agents-grid">
          <div
            v-for="agent in filteredAgents"
            :key="agent.id"
            class="agent-card"
          >
            <div class="card-header">
              <div class="agent-avatar">{{ getAgentIcon(agent.icon) }}</div>
              <div class="agent-status" :class="getStatusClass(agent.status)">
                {{ getStatusText(agent.status) }}
              </div>
            </div>
            
            <div class="card-content">
              <h3 class="agent-name">{{ agent.name }}</h3>
              <p class="agent-description">{{ agent.description }}</p>
            </div>
            
            <div class="card-meta">
              <div class="meta-item">
                <span class="meta-label">使用次数</span>
                <span class="meta-value">{{ agent.usageCount || 0 }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">模型</span>
                <span class="meta-value">{{ agent.model?.name || 'Unknown' }}</span>
              </div>
            </div>
            
            <div class="card-actions">
              <router-link 
                :to="`/chat/${agent.id}`" 
                class="action-btn primary"
              >
                测试对话
              </router-link>
              <router-link 
                :to="`/agent-editor/${agent.id}`" 
                class="action-btn secondary"
              >
                编辑
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'
import type { Agent } from '../types'
import { getAgentIcon, getStatusText } from '../utils/iconUtils'

const authStore = useAuthStore()
const agents = ref<Agent[]>([])
const loading = ref(true)
const searchQuery = ref('')

// 图标映射已从 utils/iconUtils 导入

// 过滤后的智能体
const filteredAgents = computed(() => {
  if (!searchQuery.value.trim()) return agents.value
  
  const query = searchQuery.value.toLowerCase().trim()
  return agents.value.filter(agent => 
    agent.name.toLowerCase().includes(query) ||
    agent.description.toLowerCase().includes(query)
  )
})

// 统计数据
const totalUsage = computed(() => 
  agents.value.reduce((sum, agent) => sum + (agent.usageCount || 0), 0)
)

const approvedAgents = computed(() => 
  agents.value.filter(agent => agent.status === 'approved').length
)

const pendingAgents = computed(() => 
  agents.value.filter(agent => agent.status === 'pending_review').length
)

// 工具函数已从 utils/iconUtils 导入

const getStatusClass = (status: string): string => {
  const classMap: {[key: string]: string} = {
    'approved': 'status-approved',
    'pending_review': 'status-pending',
    'rejected': 'status-rejected',
    'draft': 'status-draft'
  }
  return classMap[status] || 'status-unknown'
}

// 加载智能体
const loadAgents = async () => {
  try {
    loading.value = true
    const response = await api.get('/agents/')
    agents.value = response.data || []
  } catch (error) {
    console.error('加载智能体失败:', error)
    window.showMessage?.('加载智能体失败', 'error')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadAgents()
})
</script>

<style scoped>
.dashboard-page {
  background: #ffffff;
  min-height: calc(100vh - 64px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面头部 */
.dashboard-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 32px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0;
}

.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #1a73e8;
  color: #ffffff;
  padding: 0 16px;
  height: 36px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.create-btn:hover {
  background: #1557b0;
}

/* 统计概览 */
.stats-section {
  padding: 32px 0;
  background: #ffffff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: box-shadow 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 500;
  color: #202124;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #5f6368;
  margin-top: 4px;
}

/* 智能体管理 */
.agents-section {
  padding: 32px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0;
}

.search-container {
  position: relative;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9aa0a6;
  pointer-events: none;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 16px 0 40px;
  border: 1px solid #dadce0;
  border-radius: 18px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* 加载和空状态 */
.loading-container,
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  color: #5f6368;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.empty-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin: 0 0 24px 0;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #1a73e8;
  color: #ffffff;
  padding: 0 24px;
  height: 36px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.empty-action-btn:hover {
  background: #1557b0;
}

/* 智能体卡片 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.agent-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.agent-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-color: #dadce0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.agent-avatar {
  font-size: 24px;
}

.agent-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-approved {
  background: #e8f5e8;
  color: #1e8e3e;
}

.status-pending {
  background: #fef7e0;
  color: #ea8600;
}

.status-rejected {
  background: #fce8e6;
  color: #d93025;
}

.status-draft {
  background: #f1f3f4;
  color: #5f6368;
}

.card-content {
  margin-bottom: 16px;
}

.agent-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.agent-description {
  font-size: 14px;
  color: #5f6368;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin: 0;
}

.card-meta {
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #f1f3f4;
  border-bottom: 1px solid #f1f3f4;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-bottom: 4px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: #5f6368;
}

.meta-value {
  color: #202124;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #1a73e8;
  color: #ffffff;
}

.action-btn.primary:hover {
  background: #1557b0;
}

.action-btn.secondary {
  background: #ffffff;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.action-btn.secondary:hover {
  background: #f8f9fa;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    width: 100%;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 16px;
  }
}
</style> 