<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 返回首页 -->
      <router-link to="/" class="back-link">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        返回首页
      </router-link>

      <!-- Logo -->
      <div class="logo">AI搜索</div>

      <!-- 登录卡片 -->
      <div class="login-card">
        <h1 class="login-title">登录您的账户</h1>
        <p class="login-subtitle">继续使用AI智能体服务</p>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email" class="form-label">邮箱地址</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="form-input"
              placeholder="请输入邮箱地址"
              required
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="form-input"
              placeholder="请输入密码"
              required
              :disabled="loading"
            />
          </div>

          <button type="submit" class="login-button" :disabled="loading">
            <span v-if="!loading">登录</span>
            <div v-else class="loading-spinner"></div>
          </button>
        </form>

        <div class="divider">
          <span>或</span>
        </div>

        <div class="register-section">
          <span class="register-text">还没有账户？</span>
          <router-link to="/register" class="register-link">注册账户</router-link>
        </div>

        <!-- 演示账户提示 -->
        <div class="demo-accounts">
          <div class="demo-title">演示账户</div>
          <div class="demo-list">
            <div class="demo-item">
              <span class="demo-label">管理员:</span>
              <span class="demo-value"><EMAIL> / admin123</span>
            </div>
            <div class="demo-item">
              <span class="demo-label">普通用户:</span>
              <span class="demo-value"><EMAIL> / system123</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const form = ref({
  email: '',
  password: ''
})

const handleLogin = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    await authStore.login({
      email: form.value.email,
      password: form.value.password
    })
    window.showMessage?.('登录成功！', 'success')
    
    // 根据用户角色重定向
    if (authStore.isAdmin) {
      router.push('/admin')
    } else {
      router.push('/dashboard')
    }
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '登录失败，请检查邮箱和密码', 'error')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  padding: 24px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 24px;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: #1a73e8;
}

.logo {
  font-size: 40px;
  font-weight: 400;
  color: #1a73e8;
  margin-bottom: 32px;
  letter-spacing: -1px;
}

.login-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 48px 40px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.login-title {
  font-size: 24px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0 0 32px 0;
}

.login-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.form-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-input:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.login-button {
  width: 100%;
  height: 44px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
}

.login-button:hover:not(:disabled) {
  background: #1557b0;
}

.login-button:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e8eaed;
}

.divider span {
  background: #ffffff;
  padding: 0 16px;
  color: #5f6368;
  font-size: 14px;
}

.register-section {
  margin-bottom: 24px;
}

.register-text {
  color: #5f6368;
  font-size: 14px;
}

.register-link {
  color: #1a73e8;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
}

.register-link:hover {
  text-decoration: underline;
}

.demo-accounts {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8eaed;
  text-align: left;
}

.demo-title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 12px;
}

.demo-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-item {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-label {
  color: #5f6368;
  min-width: 60px;
}

.demo-value {
  color: #202124;
  font-family: monospace;
  background: #ffffff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e8eaed;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-page {
    padding: 16px;
  }
  
  .login-card {
    padding: 32px 24px;
  }
  
  .logo {
    font-size: 32px;
    margin-bottom: 24px;
  }
  
  .login-title {
    font-size: 20px;
  }
}
</style> 