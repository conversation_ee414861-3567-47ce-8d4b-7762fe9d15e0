<template>
  <div class="search-home">
    <!-- 搜索引擎风格的Logo -->
    <div class="search-logo">AI搜索</div>
    
    <!-- 主搜索框 -->
    <div class="search-container">
      <div class="search-box">
        <input
          v-model="searchQuery"
          @keyup.enter="handleSearch"
          type="text"
          class="search-input"
          placeholder="搜索AI智能体或提出您的问题..."
          autofocus
        />
        <button @click="handleSearch" class="search-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 热门AI智能体 -->
    <div v-if="!searchQuery" class="agents-grid">
      <router-link
        v-for="agent in displayedAgents"
        :key="agent.id"
        :to="`/chat/${agent.id}`"
        class="agent-card"
      >
        <div class="agent-icon">{{ getAgentIcon(agent.icon) }}</div>
        <div class="agent-name">{{ agent.name }}</div>
        <div class="agent-description">{{ agent.description }}</div>
        <div class="agent-meta">
          <span>{{ agent.usageCount || 0 }} 次使用</span>
          <span v-if="agent.model">{{ agent.model.name }}</span>
        </div>
      </router-link>
    </div>

    <!-- 搜索结果 -->
    <div v-else-if="filteredAgents.length > 0" class="agents-grid">
      <router-link
        v-for="agent in filteredAgents"
        :key="agent.id"
        :to="`/chat/${agent.id}`"
        class="agent-card"
      >
        <div class="agent-icon">{{ getAgentIcon(agent.icon) }}</div>
        <div class="agent-name">{{ agent.name }}</div>
        <div class="agent-description">{{ agent.description }}</div>
        <div class="agent-meta">
          <span>{{ agent.usageCount || 0 }} 次使用</span>
          <span v-if="agent.model">{{ agent.model.name }}</span>
        </div>
      </router-link>
    </div>

    <!-- 无搜索结果 -->
    <div v-else-if="searchQuery" class="no-results">
      <div class="no-results-icon">🔍</div>
      <div class="no-results-text">未找到相关的AI智能体</div>
      <div class="no-results-subtitle">尝试使用其他关键词或浏览热门智能体</div>
      <button @click="clearSearch" class="btn btn-primary">浏览全部</button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载AI智能体...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import api from '../services/api'
import type { Agent } from '../types'
import { getAgentIcon } from '../utils/iconUtils'

const router = useRouter()

const agents = ref<Agent[]>([])
const loading = ref(true)
const searchQuery = ref('')

// getAgentIcon 函数已从 utils/iconUtils 导入

// 显示的智能体（默认显示前8个）
const displayedAgents = computed(() => {
  return agents.value.slice(0, 8)
})

// 过滤后的智能体
const filteredAgents = computed(() => {
  if (!searchQuery.value.trim()) return agents.value
  
  const query = searchQuery.value.toLowerCase().trim()
  return agents.value.filter(agent => 
    agent.name.toLowerCase().includes(query) ||
    agent.description.toLowerCase().includes(query)
  )
})

// 处理搜索
const handleSearch = () => {
  if (!searchQuery.value.trim()) return
  
  // 如果找到匹配的智能体，直接进入第一个
  if (filteredAgents.value.length === 1) {
    router.push(`/chat/${filteredAgents.value[0].id}`)
  }
  // 如果没有找到智能体，可以考虑创建通用对话
  else if (filteredAgents.value.length === 0) {
    // 使用第一个可用的智能体进行通用对话
    if (agents.value.length > 0) {
      router.push(`/chat/${agents.value[0].id}?query=${encodeURIComponent(searchQuery.value)}`)
    }
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
}

// 加载公开智能体
const loadAgents = async () => {
  try {
    loading.value = true
    const response = await api.get('/public/agents')
    agents.value = response.data || []
  } catch (error) {
    console.error('加载智能体失败:', error)
    window.showMessage?.('加载智能体失败', 'error')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadAgents()
})
</script>

<style scoped>
.search-home {
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  background: #ffffff;
}

.search-logo {
  font-size: 90px;
  font-weight: 400;
  color: #1a73e8;
  margin-bottom: 32px;
  text-align: center;
  letter-spacing: -2px;
}

.search-container {
  width: 100%;
  max-width: 584px;
  margin-bottom: 32px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 44px;
  border: 1px solid #dfe1e5;
  border-radius: 24px;
  padding: 0 52px 0 16px;
  font-size: 16px;
  outline: none;
  transition: box-shadow 0.2s ease, border-color 0.2s ease;
}

.search-input:hover {
  box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
  border-color: rgba(223,225,229,0);
}

.search-input:focus {
  box-shadow: 0 2px 8px 1px rgba(64,60,67,.24);
  border-color: rgba(223,225,229,0);
}

.search-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9aa0a6;
  transition: color 0.2s ease;
  padding: 8px;
  border-radius: 50%;
}

.search-btn:hover {
  color: #1a73e8;
  background: rgba(26, 115, 232, 0.04);
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 24px 0;
}

.agent-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.agent-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #dadce0;
  transform: translateY(-2px);
}

.agent-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.agent-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.agent-description {
  font-size: 14px;
  color: #5f6368;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 12px;
}

.agent-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #70757a;
}

.no-results {
  text-align: center;
  padding: 48px 24px;
  max-width: 400px;
}

.no-results-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-results-text {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.no-results-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin-bottom: 24px;
}

.loading-container {
  text-align: center;
  padding: 48px 24px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  color: #5f6368;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-logo {
    font-size: 60px;
    margin-bottom: 24px;
  }
  
  .search-container {
    max-width: 90%;
  }
  
  .agents-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .search-home {
    padding: 0 16px;
    min-height: calc(100vh - 56px);
  }
}

@media (max-width: 480px) {
  .search-logo {
    font-size: 48px;
  }
  
  .search-input {
    font-size: 14px;
  }
}
</style> 