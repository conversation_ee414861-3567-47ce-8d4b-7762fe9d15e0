<template>
  <div class="chat-page">
    <!-- 聊天头部 -->
    <header class="chat-header">
      <div class="container">
        <div class="header-content">
          <router-link to="/dashboard" class="back-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
            </svg>
            返回
          </router-link>
          
          <div class="agent-info" v-if="agent">
            <div class="agent-avatar">{{ getAgentIcon(agent.icon) }}</div>
            <div class="agent-details">
              <h1 class="agent-name">{{ agent.name }}</h1>
              <p class="agent-description">{{ agent.description }}</p>
            </div>
          </div>
          
          <div class="header-actions">
            <button @click="clearConversation" class="clear-btn" :disabled="messages.length === 0">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
              清空对话
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 聊天主体 -->
    <main class="chat-main">
      <div class="container">
        <div class="chat-container">
          <!-- 消息列表 -->
          <div ref="messagesContainer" class="messages-container">
            <!-- 欢迎消息 -->
            <div v-if="messages.length === 0" class="welcome-section">
              <div class="welcome-avatar">{{ getAgentIcon(agent?.icon || 'bot') }}</div>
              <div class="welcome-content">
                <h2 class="welcome-title">开始与 {{ agent?.name || '智能体' }} 对话</h2>
                <p class="welcome-subtitle">{{ agent?.description || '您的AI助手已准备就绪' }}</p>
              </div>
              
              <!-- 建议问题 -->
              <div class="suggestions" v-if="suggestedQuestions.length > 0">
                <h3 class="suggestions-title">您可以尝试问：</h3>
                <div class="suggestions-grid">
                  <button
                    v-for="(question, index) in suggestedQuestions"
                    :key="index"
                    @click="sendSuggestion(question)"
                    class="suggestion-btn"
                    :disabled="loading"
                  >
                    {{ question }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 对话消息 -->
            <div v-else class="messages-list">
              <div
                v-for="message in messages"
                :key="message.id"
                :class="['message-item', message.role]"
              >
                <div class="message-content">
                  <div class="message-text">{{ message.content }}</div>
                  <div class="message-meta">
                    <span class="message-time">{{ formatTime(message.createdAt) }}</span>
                    <button @click="copyMessage(message.content)" class="copy-btn">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 正在输入指示器 -->
              <div v-if="isTyping" class="typing-indicator">
                <div class="typing-avatar">{{ getAgentIcon(agent?.icon || 'bot') }}</div>
                <div class="typing-content">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <div class="input-container">
              <textarea
                ref="messageInput"
                v-model="newMessage"
                @keydown="handleKeydown"
                @input="autoResize"
                class="message-input"
                placeholder="输入您的消息..."
                rows="1"
                :disabled="loading"
              ></textarea>
              
              <button
                @click="sendMessage"
                :disabled="!newMessage.trim() || loading"
                class="send-btn"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'
import type { Agent, Message, Conversation } from '../types'
import { getAgentIcon } from '../utils/iconUtils'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const agent = ref<Agent | null>(null)
const conversation = ref<Conversation | null>(null)
const messages = ref<Message[]>([])
const newMessage = ref('')
const loading = ref(false)
const isTyping = ref(false)

// DOM引用
const messagesContainer = ref<HTMLElement | null>(null)
const messageInput = ref<HTMLTextAreaElement | null>(null)

// 建议问题
const suggestedQuestions = ref([
  '你好，请介绍一下你自己',
  '你能帮我做什么？',
  '有什么使用建议吗？'
])

// getAgentIcon 函数已从 utils/iconUtils 导入

// 格式化时间
const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 自动调整输入框高度
const autoResize = () => {
  if (messageInput.value) {
    messageInput.value.style.height = 'auto'
    messageInput.value.style.height = Math.min(messageInput.value.scrollHeight, 120) + 'px'
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 发送建议问题
const sendSuggestion = (question: string) => {
  newMessage.value = question
  sendMessage()
}

// 发送消息
const sendMessage = async () => {
  if (!newMessage.value.trim() || loading.value || !conversation.value) return

  // 检查认证状态
  if (!authStore.isAuthenticated) {
    window.showMessage?.('请先登录', 'warning')
    router.push('/login')
    return
  }

  const userMessage = newMessage.value.trim()
  newMessage.value = ''
  loading.value = true

  // 添加用户消息
  const userMsg: Message = {
    id: Date.now(),
    conversationId: conversation.value.id,
    role: 'user',
    content: userMessage,
    createdAt: new Date().toISOString()
  }
  messages.value.push(userMsg)
  scrollToBottom()

  // 重置输入框高度
  autoResize()

  try {
    isTyping.value = true
    scrollToBottom()

    console.log('发送消息到对话:', conversation.value.id)
    const response = await api.post(`/chat/conversations/${conversation.value.id}/messages`, {
      content: userMessage
    })

    isTyping.value = false

    if (response.data) {
      messages.value.push(response.data)
      scrollToBottom()
      console.log('消息发送成功')
    }
  } catch (error: any) {
    console.error('发送消息失败:', error)
    isTyping.value = false
    
    if (error.response?.status === 401) {
      window.showMessage?.('登录已过期，请重新登录', 'warning')
      authStore.logout()
      router.push('/login')
    } else {
      window.showMessage?.(error.response?.data?.error || '发送消息失败', 'error')
      // 移除发送失败的用户消息
      messages.value.pop()
    }
  } finally {
    loading.value = false
  }
}

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    window.showMessage?.('消息已复制到剪贴板', 'success')
  } catch (error) {
    console.error('复制失败:', error)
    window.showMessage?.('复制失败', 'error')
  }
}

// 清空对话
const clearConversation = async () => {
  if (!conversation.value || messages.value.length === 0) return
  
  if (!confirm('确定要清空当前对话吗？此操作不可撤销。')) return

  try {
    await api.delete(`/chat/conversations/${conversation.value.id}/messages`)
    messages.value = []
    window.showMessage?.('对话已清空', 'success')
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '清空失败', 'error')
  }
}

// 初始化对话
const initializeChat = async () => {
  const agentId = route.params.id
  if (!agentId) {
    router.push('/dashboard')
    return
  }

  // 检查认证状态
  if (!authStore.isAuthenticated) {
    console.log('用户未认证，跳转到登录页')
    window.showMessage?.('请先登录', 'warning')
    router.push('/login')
    return
  }

  try {
    // 加载智能体信息
    const agentResponse = await api.get(`/public/agents/${agentId}`)
    agent.value = agentResponse.data

    // 创建或获取对话
    console.log('创建对话，智能体ID:', agentId)
    const conversationResponse = await api.post('/chat/conversations', {
      agentId: Number(agentId)
    })
    conversation.value = conversationResponse.data
    console.log('对话创建成功:', conversation.value?.id)

    // 加载历史消息
    if (conversation.value?.id) {
      const messagesResponse = await api.get(`/chat/conversations/${conversation.value.id}/messages`)
      messages.value = messagesResponse.data || []
      
      if (messages.value.length > 0) {
        scrollToBottom()
      }
    }
  } catch (error: any) {
    console.error('初始化聊天失败:', error)
    if (error.response?.status === 401) {
      window.showMessage?.('登录已过期，请重新登录', 'warning')
      authStore.logout()
      router.push('/login')
    } else {
      window.showMessage?.('加载聊天失败', 'error')
      router.push('/dashboard')
    }
  }
}

onMounted(() => {
  initializeChat()
})
</script>

<style scoped>
.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  width: 100%;
}

/* 聊天头部 */
.chat-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 0;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.back-btn:hover {
  color: #1a73e8;
  background: #f8f9fa;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.agent-avatar {
  font-size: 32px;
  flex-shrink: 0;
}

.agent-details {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agent-description {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-actions {
  flex-shrink: 0;
}

.clear-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #ffffff;
  color: #5f6368;
  border: 1px solid #dadce0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover:not(:disabled) {
  background: #f8f9fa;
  color: #3c4043;
}

.clear-btn:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

/* 聊天主体 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  height: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 0;
  min-height: 0;
}

/* 欢迎部分 */
.welcome-section {
  text-align: center;
  padding: 48px 24px;
  max-width: 600px;
  margin: 0 auto;
}

.welcome-avatar {
  font-size: 64px;
  margin-bottom: 16px;
}

.welcome-content {
  margin-bottom: 32px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0;
}

.suggestions {
  max-width: 500px;
  margin: 0 auto;
}

.suggestions-title {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 16px 0;
}

.suggestions-grid {
  display: grid;
  gap: 8px;
}

.suggestion-btn {
  background: #f8f9fa;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 12px 16px;
  text-align: left;
  color: #3c4043;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.suggestion-btn:hover:not(:disabled) {
  background: #f1f3f4;
  border-color: #dadce0;
}

.suggestion-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  flex-direction: column;
}

.message-item.user {
  align-items: flex-end;
}

.message-item.assistant {
  align-items: flex-start;
}

.message-content {
  max-width: 70%;
  position: relative;
}

.message-item.user .message-content {
  background: #1a73e8;
  color: #ffffff;
  border-radius: 16px 4px 16px 16px;
}

.message-item.assistant .message-content {
  background: #f8f9fa;
  color: #202124;
  border-radius: 4px 16px 16px 16px;
  border: 1px solid #e8eaed;
}

.message-text {
  padding: 12px 16px;
  line-height: 1.4;
  word-break: break-word;
  white-space: pre-wrap;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 16px 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-content:hover .message-meta {
  opacity: 1;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
}

.copy-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0.5;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

.message-item.user .copy-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.typing-avatar {
  font-size: 24px;
  flex-shrink: 0;
}

.typing-content {
  background: #f8f9fa;
  border: 1px solid #e8eaed;
  border-radius: 4px 16px 16px 16px;
  padding: 12px 16px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #9aa0a6;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-section {
  padding: 16px 0;
  border-top: 1px solid #e8eaed;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid #dadce0;
  border-radius: 22px;
  font-size: 14px;
  outline: none;
  resize: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
  line-height: 1.4;
}

.message-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.message-input:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.send-btn {
  width: 44px;
  height: 44px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background: #1557b0;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    gap: 12px;
  }

  .agent-name {
    font-size: 18px;
  }

  .agent-description {
    font-size: 12px;
  }

  .welcome-section {
    padding: 32px 16px;
  }

  .welcome-avatar {
    font-size: 48px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .message-content {
    max-width: 85%;
  }

  .suggestions-grid {
    gap: 6px;
  }

  .suggestion-btn {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .agent-info {
    gap: 8px;
  }

  .agent-avatar {
    font-size: 24px;
  }

  .agent-name {
    font-size: 16px;
  }

  .clear-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .clear-btn svg {
    width: 14px;
    height: 14px;
  }

  .message-content {
    max-width: 90%;
  }

  .message-text {
    padding: 10px 12px;
    font-size: 14px;
  }

  .input-container {
    gap: 6px;
  }

  .send-btn {
    width: 40px;
    height: 40px;
  }
}
</style> 