<template>
  <div class="agent-editor-page">
    <!-- 页面头部 -->
    <header class="editor-header">
      <div class="container">
        <div class="header-content">
          <div class="header-title-section">
            <h1 class="page-title">{{ isEdit ? '编辑智能体' : '创建智能体' }}</h1>
            <p class="page-subtitle">{{ isEdit ? '修改您的智能体设置' : '配置您的AI助手' }}</p>
          </div>
          
          <div class="header-actions">
            <router-link to="/dashboard" class="back-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              返回控制台
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="editor-main">
      <div class="container">
        <div class="editor-layout">
          <!-- 主表单区域 -->
          <div class="form-section">
            <form @submit.prevent="handleSubmit" class="agent-form">
              <!-- 基本信息 -->
              <div class="form-card">
                <h2 class="card-title">基本信息</h2>
                
                <div class="form-group">
                  <label for="name" class="form-label">智能体名称</label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    class="form-input"
                    placeholder="为您的智能体起一个名字"
                    required
                    :disabled="loading"
                  />
                </div>

                <div class="form-group">
                  <label for="description" class="form-label">描述</label>
                  <textarea
                    id="description"
                    v-model="form.description"
                    class="form-textarea"
                    placeholder="简单描述一下这个智能体的功能和特点"
                    rows="3"
                    required
                    :disabled="loading"
                  ></textarea>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="icon" class="form-label">图标</label>
                    <select
                      id="icon"
                      v-model="form.icon"
                      class="form-select"
                      :disabled="loading"
                    >
                      <option value="bot">🤖 机器人</option>
                      <option value="assistant">🧠 助手</option>
                      <option value="code">💻 编程</option>
                      <option value="write">✍️ 写作</option>
                      <option value="creative">🎨 创意</option>
                      <option value="business">💼 商务</option>
                      <option value="education">📚 教育</option>
                      <option value="travel">✈️ 旅行</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label for="model" class="form-label">AI模型</label>
                    <select
                      id="model"
                      v-model.number="form.modelId"
                      class="form-select"
                      required
                      :disabled="loading"
                    >
                      <option value="0">请选择模型</option>
                      <option
                        v-for="model in models"
                        :key="model.id"
                        :value="model.id"
                      >
                        {{ model.name }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 系统提示 -->
              <div class="form-card">
                <h2 class="card-title">系统提示</h2>
                <p class="card-description">定义智能体的角色、行为和回复风格</p>
                
                <div class="form-group">
                  <label for="systemPrompt" class="form-label">提示词</label>
                  <textarea
                    id="systemPrompt"
                    v-model="form.systemPrompt"
                    class="form-textarea large"
                    placeholder="例如：你是一个专业的客服助手，请用友善、专业的语调回答用户问题..."
                    rows="8"
                    required
                    :disabled="loading"
                  ></textarea>
                  <div class="input-hint">
                    提示：详细的系统提示有助于智能体更好地理解其角色和任务
                  </div>
                </div>
              </div>

              <!-- 发布设置 -->
              <div class="form-card">
                <h2 class="card-title">发布设置</h2>
                
                <div class="form-group">
                  <div class="toggle-group">
                    <label class="toggle-wrapper">
                      <input
                        v-model="form.isPublic"
                        type="checkbox"
                        class="toggle-input"
                        :disabled="loading"
                      />
                      <span class="toggle-slider"></span>
                    </label>
                    <div class="toggle-label">
                      <div class="toggle-title">公开智能体</div>
                      <div class="toggle-description">
                        {{ form.isPublic ? '其他用户可以发现和使用' : '仅您可以使用' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="form-actions">
                <button type="button" @click="resetForm" class="reset-btn" :disabled="loading">
                  重置
                </button>
                <button type="submit" class="submit-btn" :disabled="!canSubmit || loading">
                  <span v-if="!loading">{{ isEdit ? '保存更改' : '创建智能体' }}</span>
                  <div v-else class="loading-spinner"></div>
                </button>
              </div>
            </form>
          </div>

          <!-- 侧边栏预览 -->
          <div class="preview-section">
            <div class="preview-card">
              <h3 class="preview-title">预览</h3>
              
              <div class="agent-preview">
                <div class="preview-avatar">{{ getIconEmoji(form.icon) }}</div>
                <div class="preview-info">
                  <div class="preview-name">{{ form.name || '智能体名称' }}</div>
                  <div class="preview-description">
                    {{ form.description || '智能体描述' }}
                  </div>
                  <div class="preview-meta">
                    <span class="preview-model">{{ getSelectedModelName() || '未选择模型' }}</span>
                    <span class="preview-status">
                      {{ form.isPublic ? '公开' : '私有' }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="preview-actions">
                <div class="preview-btn">测试对话</div>
              </div>

              <!-- 提示 -->
              <div class="tips-section">
                <h4 class="tips-title">创建提示</h4>
                <ul class="tips-list">
                  <li>为智能体起一个简洁明了的名称</li>
                  <li>用一句话描述智能体的核心功能</li>
                  <li>系统提示词决定了对话风格和能力</li>
                  <li>选择合适的AI模型以获得最佳效果</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import api, { agentApi } from '../services/api'
import type { Agent, BaseModel } from '../types'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单状态
const loading = ref(false)
const models = ref<BaseModel[]>([])

// 表单数据
const form = ref({
  name: '',
  description: '',
  icon: 'bot',
  modelId: 0,
  systemPrompt: '',
  isPublic: false
})

// 原始表单数据（用于重置）
const originalForm = ref({ ...form.value })

// 图标映射
const iconMap: Record<string, string> = {
  bot: '🤖',
  assistant: '🧠',
  code: '💻',
  write: '✍️',
  creative: '🎨',
  business: '💼',
  education: '📚',
  travel: '✈️'
}

// 表单验证
const canSubmit = computed(() => {
  return form.value.name.trim() &&
         form.value.description.trim() &&
         form.value.systemPrompt.trim() &&
         form.value.modelId > 0 &&
         !loading.value
})

// 获取图标表情
const getIconEmoji = (icon: string): string => {
  return iconMap[icon] || '🤖'
}

// 获取选中的模型名称
const getSelectedModelName = (): string => {
  const selectedModel = models.value.find((m: BaseModel) => m.id === form.value.modelId)
  return selectedModel?.name || ''
}

// 重置表单
const resetForm = () => {
  form.value = { ...originalForm.value }
}

// 加载模型列表
const loadModels = async () => {
  try {
    const response = await agentApi.getModels()
    models.value = Array.isArray(response) ? response : []
    console.log('加载的模型列表:', models.value)
  } catch (error) {
    console.error('加载模型失败:', error)
    window.showMessage?.('加载模型失败', 'error')
    models.value = []
  }
}

// 加载智能体数据（编辑模式）
const loadAgent = async (id: string) => {
  try {
    loading.value = true
    const response = await api.get(`/agents/${id}`)
    const agent: Agent = response.data
    
    form.value = {
      name: agent.name,
      description: agent.description,
      icon: agent.icon || 'bot',
      modelId: agent.modelId,
      systemPrompt: agent.persona || '',
      isPublic: agent.isPublic || false
    }
    
    originalForm.value = { ...form.value }
  } catch (error) {
    console.error('加载智能体失败:', error)
    window.showMessage?.('加载智能体失败', 'error')
    router.push('/dashboard')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!canSubmit.value) return

  loading.value = true
  try {
    const data = {
      name: form.value.name.trim(),
      description: form.value.description.trim(),
      icon: form.value.icon,
      modelId: form.value.modelId,
      persona: form.value.systemPrompt.trim(),
      isPublic: form.value.isPublic
    }

    if (isEdit.value) {
      await api.put(`/agents/${route.params.id}`, data)
      window.showMessage?.('智能体更新成功', 'success')
    } else {
      await api.post('/agents/', data)
      window.showMessage?.('智能体创建成功', 'success')
    }

    router.push('/dashboard')
  } catch (error: any) {
    window.showMessage?.(
      error.response?.data?.error || `${isEdit.value ? '更新' : '创建'}失败，请重试`,
      'error'
    )
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(async () => {
  await loadModels()
  
  if (isEdit.value && route.params.id) {
    await loadAgent(route.params.id as string)
  } else {
    originalForm.value = { ...form.value }
  }
})
</script>

<style scoped>
.agent-editor-page {
  background: #ffffff;
  min-height: calc(100vh - 64px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面头部 */
.editor-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 32px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  color: #1a73e8;
  background: #f8f9fa;
}

/* 主要内容 */
.editor-main {
  padding: 32px 0;
}

.editor-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 32px;
}

/* 表单区域 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 24px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.card-description {
  font-size: 14px;
  color: #5f6368;
  margin: 0 0 24px 0;
}

.agent-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
}

.form-input,
.form-select {
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.form-textarea {
  padding: 12px 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.form-textarea.large {
  min-height: 200px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.input-hint {
  font-size: 12px;
  color: #5f6368;
  font-style: italic;
}

/* 切换开关 */
.toggle-group {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.toggle-wrapper {
  position: relative;
  cursor: pointer;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  display: block;
  width: 44px;
  height: 24px;
  background: #dadce0;
  border-radius: 12px;
  position: relative;
  transition: background-color 0.2s ease;
}

.toggle-slider::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.toggle-input:checked + .toggle-slider {
  background: #1a73e8;
}

.toggle-input:checked + .toggle-slider::after {
  transform: translateX(20px);
}

.toggle-label {
  flex: 1;
}

.toggle-title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 2px;
}

.toggle-description {
  font-size: 12px;
  color: #5f6368;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e8eaed;
}

.reset-btn,
.submit-btn {
  height: 44px;
  padding: 0 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
}

.reset-btn {
  background: #ffffff;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.reset-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.submit-btn {
  background: #1a73e8;
  color: #ffffff;
  border: none;
}

.submit-btn:hover:not(:disabled) {
  background: #1557b0;
}

.reset-btn:disabled,
.submit-btn:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 预览区域 */
.preview-section {
  position: sticky;
  top: 24px;
  height: fit-content;
}

.preview-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 24px;
}

.preview-title {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 20px 0;
}

.agent-preview {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-avatar {
  font-size: 24px;
  flex-shrink: 0;
}

.preview-info {
  flex: 1;
  min-width: 0;
}

.preview-name {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
  truncate: true;
}

.preview-description {
  font-size: 12px;
  color: #5f6368;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 8px;
}

.preview-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-model,
.preview-status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #e8eaed;
  color: #3c4043;
}

.preview-actions {
  margin-bottom: 20px;
}

.preview-btn {
  width: 100%;
  height: 36px;
  background: #f8f9fa;
  color: #3c4043;
  border: 1px solid #dadce0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: 500;
  cursor: not-allowed;
}

.tips-section {
  border-top: 1px solid #e8eaed;
  padding-top: 20px;
}

.tips-title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 12px 0;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-list li {
  font-size: 12px;
  color: #5f6368;
  line-height: 1.4;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.tips-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1a73e8;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .editor-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .preview-section {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-title {
    font-size: 24px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .reset-btn,
  .submit-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .form-card {
    padding: 20px;
  }

  .agent-preview {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .preview-info {
    width: 100%;
  }
}
</style> 