<template>
  <div class="register-page">
    <div class="register-container">
      <!-- 返回首页 -->
      <router-link to="/" class="back-link">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        返回首页
      </router-link>

      <!-- Logo -->
      <div class="logo">AI搜索</div>

      <!-- 注册卡片 -->
      <div class="register-card">
        <h1 class="register-title">创建新账户</h1>
        <p class="register-subtitle">加入AI智能体平台</p>

        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <label for="nickname" class="form-label">昵称</label>
            <input
              id="nickname"
              v-model="form.nickname"
              type="text"
              class="form-input"
              placeholder="请输入昵称"
              required
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="email" class="form-label">邮箱地址</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="form-input"
              placeholder="请输入邮箱地址"
              required
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="form-input"
              placeholder="请输入密码（至少6位）"
              required
              minlength="6"
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="confirmPassword" class="form-label">确认密码</label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              class="form-input"
              placeholder="请再次输入密码"
              required
              :disabled="loading"
            />
            <div v-if="passwordMismatch" class="field-error">密码不匹配</div>
          </div>

          <!-- 服务条款 -->
          <div class="terms-section">
            <label class="checkbox-wrapper">
              <input v-model="form.agreeToTerms" type="checkbox" class="checkbox-input" required>
              <span class="checkbox-custom"></span>
              <span class="checkbox-label">
                我已阅读并同意
                <a href="#" class="terms-link">服务条款</a>
                和
                <a href="#" class="terms-link">隐私政策</a>
              </span>
            </label>
          </div>

          <button type="submit" class="register-button" :disabled="!canSubmit || loading">
            <span v-if="!loading">创建账户</span>
            <div v-else class="loading-spinner"></div>
          </button>
        </form>

        <div class="divider">
          <span>或</span>
        </div>

        <div class="login-section">
          <span class="login-text">已有账户？</span>
          <router-link to="/login" class="login-link">立即登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const form = ref({
  nickname: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

// 密码匹配检查
const passwordMismatch = computed(() => {
  return form.value.confirmPassword && form.value.password !== form.value.confirmPassword
})

// 表单验证
const canSubmit = computed(() => {
  return (
    form.value.nickname.trim() &&
    form.value.email.trim() &&
    form.value.password.length >= 6 &&
    form.value.password === form.value.confirmPassword &&
    form.value.agreeToTerms &&
    !loading.value
  )
})

const handleRegister = async () => {
  if (!canSubmit.value) return
  
  loading.value = true
  try {
    await authStore.register({
      nickname: form.value.nickname,
      email: form.value.email,
      password: form.value.password
    })
    
    window.showMessage?.('注册成功！欢迎加入AI搜索！', 'success')
    router.push('/dashboard')
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '注册失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  padding: 24px;
}

.register-container {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 24px;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: #1a73e8;
}

.logo {
  font-size: 40px;
  font-weight: 400;
  color: #1a73e8;
  margin-bottom: 32px;
  letter-spacing: -1px;
}

.register-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 48px 40px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.register-title {
  font-size: 24px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 8px 0;
}

.register-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0 0 32px 0;
}

.register-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.form-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-input:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.field-error {
  color: #d93025;
  font-size: 12px;
  margin-top: 4px;
}

.terms-section {
  margin: 16px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  line-height: 1.4;
  text-align: left;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #dadce0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-input:checked + .checkbox-custom {
  background: #1a73e8;
  border-color: #1a73e8;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
  color: #5f6368;
  font-weight: 400;
}

.terms-link {
  color: #1a73e8;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

.register-button {
  width: 100%;
  height: 44px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
}

.register-button:hover:not(:disabled) {
  background: #1557b0;
}

.register-button:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e8eaed;
}

.divider span {
  background: #ffffff;
  padding: 0 16px;
  color: #5f6368;
  font-size: 14px;
}

.login-section {
  margin-bottom: 24px;
}

.login-text {
  color: #5f6368;
  font-size: 14px;
}

.login-link {
  color: #1a73e8;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
}

.login-link:hover {
  text-decoration: underline;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-page {
    padding: 16px;
  }
  
  .register-card {
    padding: 32px 24px;
  }
  
  .logo {
    font-size: 32px;
    margin-bottom: 24px;
  }
  
  .register-title {
    font-size: 20px;
  }
}
</style> 