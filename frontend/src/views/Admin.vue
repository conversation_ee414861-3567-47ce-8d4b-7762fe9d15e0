<template>
  <div class="admin-page">
    <!-- 页面头部 -->
    <header class="admin-header">
      <div class="container">
        <div class="header-content">
          <div class="header-title-section">
            <h1 class="page-title">管理中心</h1>
            <p class="page-subtitle">系统管理和配置</p>
          </div>
          
          <div class="header-actions">
            <router-link to="/dashboard" class="back-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              返回控制台
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="admin-main">
      <div class="container">
        <!-- 导航标签 -->
        <nav class="tab-nav">
          <button 
            v-for="tab in tabs" 
            :key="tab.id"
            @click="handleTabChange(tab.id)"
            :class="['tab-btn', { active: activeTab === tab.id }]"
          >
            {{ tab.name }}
          </button>
        </nav>

        <!-- 标签内容 -->
        <div class="tab-content">
          <!-- 智能体审核 -->
          <div v-if="activeTab === 'agents'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">智能体审核</h2>
              <p class="panel-subtitle">审核和管理用户提交的智能体</p>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading.agents" class="loading-container">
              <div class="loading-spinner"></div>
              <p class="loading-text">正在加载智能体...</p>
            </div>

            <!-- 智能体列表 -->
            <div v-else>
              <div v-if="agents.length === 0" class="empty-state">
                <div class="empty-icon">🤖</div>
                <h3 class="empty-title">暂无待审核智能体</h3>
                <p class="empty-subtitle">所有智能体都已处理完成</p>
              </div>

              <div v-else class="agent-cards">
                <div
                  v-for="agent in agents"
                  :key="agent.id"
                  class="agent-card"
                >
                  <div class="agent-info">
                    <div class="agent-header">
                      <div class="agent-avatar">{{ getAgentIcon(agent.icon) }}</div>
                      <div class="agent-basic">
                        <h3 class="agent-name">{{ agent.name }}</h3>
                        <p class="agent-description">{{ agent.description }}</p>
                      </div>
                    </div>
                    <div class="agent-meta">
                      <span class="meta-item">创建者: {{ agent.creator?.nickname || agent.creator?.email }}</span>
                      <span class="meta-item">创建时间: {{ formatDate(agent.createdAt) }}</span>
                      <span class="meta-item status" :class="agent.status">状态: {{ getStatusText(agent.status) }}</span>
                    </div>
                  </div>
                  
                  <div class="agent-actions">
                    <template v-if="agent.status === 'pending_review'">
                      <button @click="approveAgent(agent.id)" class="action-btn approve">通过</button>
                      <button @click="rejectAgent(agent.id)" class="action-btn reject">拒绝</button>
                    </template>
                    <template v-else-if="agent.status === 'approved'">
                      <span class="status-badge approved">已通过</span>
                    </template>
                    <template v-else-if="agent.status === 'rejected'">
                      <span class="status-badge rejected">已拒绝</span>
                      <button @click="approveAgent(agent.id)" class="action-btn approve">重新审核</button>
                    </template>
                    <template v-else>
                      <span class="status-badge draft">草稿</span>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户管理 -->
          <div v-if="activeTab === 'users'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">用户管理</h2>
              <p class="panel-subtitle">管理平台用户账户</p>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading.users" class="loading-container">
              <div class="loading-spinner"></div>
              <p class="loading-text">正在加载用户...</p>
            </div>

            <!-- 用户列表 -->
            <div v-else class="users-table">
              <div v-if="users.length === 0" class="empty-state">
                <div class="empty-icon">👤</div>
                <h3 class="empty-title">暂无用户数据</h3>
                <p class="empty-subtitle">还没有注册用户</p>
              </div>

              <div v-else class="table-container">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th>用户</th>
                      <th>邮箱</th>
                      <th>角色</th>
                      <th>状态</th>
                      <th>注册时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="user in users" :key="user.id">
                      <td>
                        <div class="user-info">
                          <div class="user-avatar">{{ user.nickname?.[0]?.toUpperCase() || user.email[0].toUpperCase() }}</div>
                          <span class="user-name">{{ user.nickname || '未设置' }}</span>
                        </div>
                      </td>
                      <td>{{ user.email }}</td>
                      <td>
                        <span class="role-badge" :class="getUserRole(user.email)">{{ getRoleText(getUserRole(user.email)) }}</span>
                      </td>
                      <td>
                        <span class="status-badge" :class="{ banned: user.isBanned }">
                          {{ user.isBanned ? '已封禁' : '正常' }}
                        </span>
                      </td>
                      <td>{{ formatDate(user.createdAt) }}</td>
                      <td>
                        <div class="table-actions">
                          <button 
                            @click="toggleUserStatus(user.id, user.isBanned)"
                            :class="['action-btn', user.isBanned ? 'approve' : 'reject']"
                          >
                            {{ user.isBanned ? '解封' : '封禁' }}
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 交易记录 -->
          <div v-if="activeTab === 'transactions'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">交易记录</h2>
              <p class="panel-subtitle">查看平台交易数据</p>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading.transactions" class="loading-container">
              <div class="loading-spinner"></div>
              <p class="loading-text">正在加载交易记录...</p>
            </div>

            <!-- 交易列表 -->
            <div v-else class="transactions-list">
              <div v-if="transactions.length === 0" class="empty-state">
                <div class="empty-icon">💳</div>
                <h3 class="empty-title">暂无交易记录</h3>
                <p class="empty-subtitle">还没有产生交易</p>
              </div>

              <div v-else class="transaction-items">
                <div
                  v-for="transaction in transactions"
                  :key="transaction.id"
                  class="transaction-item"
                >
                  <div class="transaction-info">
                    <div class="transaction-title">{{ transaction.description || '智能体使用' }}</div>
                    <div class="transaction-meta">
                      <span>用户: {{ transaction.user?.nickname || transaction.user?.email }}</span>
                      <span>智能体: {{ transaction.agent?.name }}</span>
                      <span>时间: {{ formatDate(transaction.createdAt) }}</span>
                    </div>
                  </div>
                  <div class="transaction-amount">{{ transaction.tokenAmount }} tokens</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模型管理 -->
          <div v-if="activeTab === 'models'" class="tab-panel">
            <div class="panel-header">
              <h2 class="panel-title">模型管理</h2>
              <p class="panel-subtitle">管理AI模型配置</p>
              
              <div class="panel-actions">
                <button @click="showCreateModel = true" class="create-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  </svg>
                  添加模型
                </button>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading.models" class="loading-container">
              <div class="loading-spinner"></div>
              <p class="loading-text">正在加载模型...</p>
            </div>

            <!-- 模型列表 -->
            <div v-else class="models-grid">
              <div v-if="models.length === 0" class="empty-state">
                <div class="empty-icon">🧠</div>
                <h3 class="empty-title">暂无AI模型</h3>
                <p class="empty-subtitle">开始添加您的第一个模型</p>
              </div>

              <div v-else class="model-cards">
                <div
                  v-for="model in models"
                  :key="model.id"
                  class="model-card"
                >
                  <div class="model-info">
                    <h3 class="model-name">{{ model.name }}</h3>
                    <p class="model-api">{{ model.modelName }}</p>
                    <div class="model-costs">
                      <span class="cost-item">输入: ¥{{ model.inputCost }}/1K tokens</span>
                      <span class="cost-item">输出: ¥{{ model.outputCost }}/1K tokens</span>
                    </div>
                  </div>
                  
                  <div class="model-actions">
                    <button @click="editModel(model)" class="action-btn edit">编辑</button>
                    <button @click="deleteModel(model.id)" class="action-btn delete">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 创建模型弹窗 -->
    <div v-if="showCreateModel" class="modal-overlay" @click="showCreateModel = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">添加AI模型</h3>
          <button @click="showCreateModel = false" class="modal-close">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="createModel" class="modal-form">
          <div class="form-group">
            <label for="modelName" class="form-label">模型名称</label>
            <input
              id="modelName"
              v-model="modelForm.name"
              type="text"
              class="form-input"
              placeholder="例如：GPT-4"
              required
            />
          </div>

          <div class="form-group">
            <label for="modelApiName" class="form-label">API模型名</label>
            <input
              id="modelApiName"
              v-model="modelForm.modelName"
              type="text"
              class="form-input"
              placeholder="例如：gpt-4"
              required
            />
          </div>

          <div class="form-group">
            <label for="modelUrl" class="form-label">API地址</label>
            <input
              id="modelUrl"
              v-model="modelForm.url"
              type="url"
              class="form-input"
              placeholder="https://api.openai.com/v1"
              required
            />
          </div>

          <div class="form-group">
            <label for="modelApiKey" class="form-label">API密钥</label>
            <input
              id="modelApiKey"
              v-model="modelForm.apiKey"
              type="password"
              class="form-input"
              placeholder="sk-..."
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="inputCost" class="form-label">输入成本</label>
              <input
                id="inputCost"
                v-model.number="modelForm.inputCost"
                type="number"
                step="0.001"
                class="form-input"
                placeholder="0.03"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="outputCost" class="form-label">输出成本</label>
              <input
                id="outputCost"
                v-model.number="modelForm.outputCost"
                type="number"
                step="0.001"
                class="form-input"
                placeholder="0.06"
                required
              />
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="showCreateModel = false" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="modelSubmitting">
              <span v-if="!modelSubmitting">{{ editingModel ? '保存' : '创建' }}</span>
              <div v-else class="loading-spinner"></div>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import api, { adminApi } from '../services/api'
import type { Agent, User, Transaction, BaseModel } from '../types'
import { getAgentIcon, getRoleText, getStatusText, formatDate } from '../utils/iconUtils'

// 当前选中的标签 - 从localStorage恢复或默认为agents
const getInitialTab = () => {
  const savedTab = localStorage.getItem('admin-active-tab')
  const validTabs = ['agents', 'users', 'transactions', 'models']
  return savedTab && validTabs.includes(savedTab) ? savedTab : 'agents'
}
const activeTab = ref(getInitialTab())

// 标签配置
const tabs = [
  { id: 'agents', name: '智能体审核' },
  { id: 'users', name: '用户管理' },
  { id: 'transactions', name: '交易记录' },
  { id: 'models', name: '模型管理' }
]

// 数据状态
const agents = ref<Agent[]>([])
const users = ref<User[]>([])
const transactions = ref<Transaction[]>([])
const models = ref<BaseModel[]>([])

// 加载状态
const loading = ref({
  agents: false,
  users: false,
  transactions: false,
  models: false
})

// 模型管理
const showCreateModel = ref(false)
const editingModel = ref<BaseModel | null>(null)
const modelSubmitting = ref(false)

const modelForm = ref({
  name: '',
  modelName: '',
  url: '',
  apiKey: '',
  inputCost: 0,
  outputCost: 0
})

// 工具函数已从 utils/iconUtils 导入

// 加载数据方法
const loadAgents = async () => {
  loading.value.agents = true
  try {
        const agents_data = await adminApi.getPendingAgents()
    agents.value = agents_data || []
  } catch (error) {
    console.error('加载智能体失败:', error)
    window.showMessage?.('加载智能体失败', 'error')
  } finally {
    loading.value.agents = false
  }
}

const loadUsers = async () => {
  loading.value.users = true
  try {
    console.log('正在加载用户数据...')
    const response = await adminApi.getUsers(1, 100)
    console.log('用户API响应:', response)
    users.value = response.users || []
    console.log('设置用户数据:', users.value)
  } catch (error) {
    console.error('加载用户失败:', error)
    window.showMessage?.('加载用户失败', 'error')
  } finally {
    loading.value.users = false
  }
}

const loadTransactions = async () => {
  loading.value.transactions = true
  try {
    const response = await adminApi.getAllTransactions(1, 100)
    transactions.value = response.data || []
  } catch (error) {
    console.error('加载交易记录失败:', error)
    window.showMessage?.('加载交易记录失败', 'error')
  } finally {
    loading.value.transactions = false
  }
}

const loadModels = async () => {
  loading.value.models = true
  try {
    const response = await adminApi.getAllModels()
    models.value = response.models || []
  } catch (error) {
    console.error('加载模型失败:', error)
    window.showMessage?.('加载模型失败', 'error')
  } finally {
    loading.value.models = false
  }
}

// 智能体审核操作
const approveAgent = async (agentId: number) => {
  try {
    await adminApi.approveAgent(agentId)
    window.showMessage?.('智能体已通过审核', 'success')
    loadAgents()
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '操作失败', 'error')
  }
}

const rejectAgent = async (agentId: number) => {
  const reason = prompt('请输入拒绝原因：')
  if (!reason || !reason.trim()) return
  
  try {
    await adminApi.rejectAgent(agentId, reason.trim())
    window.showMessage?.('智能体已被拒绝', 'success')
    loadAgents()
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '操作失败', 'error')
  }
}

// 用户管理操作
const toggleUserStatus = async (userId: number, isBanned: boolean) => {
  const action = isBanned ? '解封' : '封禁'
  if (!confirm(`确定要${action}这个用户吗？`)) return
  
  try {
    if (isBanned) {
      await adminApi.unbanUser(userId)
    } else {
      await adminApi.banUser(userId)
    }
    window.showMessage?.(`用户已${action}`, 'success')
    loadUsers()
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '操作失败', 'error')
  }
}

// 模型管理操作
const resetModelForm = () => {
  modelForm.value = {
    name: '',
    modelName: '',
    url: '',
    apiKey: '',
    inputCost: 0,
    outputCost: 0
  }
  editingModel.value = null
}

const editModel = (model: BaseModel) => {
  editingModel.value = model
  modelForm.value = {
    name: model.name,
    modelName: model.modelName,
    url: model.url,
    apiKey: model.apiKey,
    inputCost: model.inputCost,
    outputCost: model.outputCost
  }
  showCreateModel.value = true
}

const createModel = async () => {
  modelSubmitting.value = true
  try {
    if (editingModel.value) {
      await adminApi.updateModel(editingModel.value.id, modelForm.value)
      window.showMessage?.('模型更新成功', 'success')
    } else {
      await adminApi.createModel(modelForm.value)
      window.showMessage?.('模型创建成功', 'success')
    }
    
    showCreateModel.value = false
    resetModelForm()
    loadModels()
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '操作失败', 'error')
  } finally {
    modelSubmitting.value = false
  }
}

const deleteModel = async (modelId: number) => {
  if (!confirm('确定要删除这个模型吗？删除后所有使用该模型的智能体将无法正常工作。')) return
  
  try {
    await adminApi.deleteModel(modelId)
    window.showMessage?.('模型删除成功', 'success')
    loadModels()
  } catch (error: any) {
    window.showMessage?.(error.response?.data?.error || '删除失败', 'error')
  }
}

// 根据邮箱判断用户角色
const getUserRole = (email: string): string => {
  return email === '<EMAIL>' ? 'admin' : 'user'
}

// 监听标签切换
const watchTab = (tabId: string) => {
  console.log('切换到标签:', tabId)
  switch (tabId) {
    case 'agents':
      console.log('智能体数据长度:', agents.value.length)
      if (agents.value.length === 0) loadAgents()
      break
    case 'users':
      console.log('用户数据长度:', users.value.length)
      console.log('当前用户数据:', users.value)
      // 强制加载用户数据，不检查长度
      loadUsers()
      break
    case 'transactions':
      console.log('交易数据长度:', transactions.value.length)
      if (transactions.value.length === 0) loadTransactions()
      break
    case 'models':
      console.log('模型数据长度:', models.value.length)
      if (models.value.length === 0) loadModels()
      break
  }
}

// 监听activeTab变化
const handleTabChange = (newTab: string) => {
  activeTab.value = newTab
  watchTab(newTab)
  localStorage.setItem('admin-active-tab', newTab)
}

onMounted(() => {
  // 根据当前标签加载对应数据
  watchTab(activeTab.value)
})
</script>

<style scoped>
.admin-page {
  background: #ffffff;
  min-height: calc(100vh - 64px);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面头部 */
.admin-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 32px;
  font-weight: 400;
  color: #202124;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #5f6368;
  margin: 0;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  color: #1a73e8;
  background: #f8f9fa;
}

/* 主要内容 */
.admin-main {
  padding: 32px 0;
}

/* 导航标签 */
.tab-nav {
  display: flex;
  border-bottom: 1px solid #e8eaed;
  margin-bottom: 32px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 0;
  margin-right: 32px;
  font-size: 14px;
  color: #5f6368;
  cursor: pointer;
  position: relative;
  transition: color 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: #1a73e8;
}

.tab-btn.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

/* 标签内容 */
.tab-content {
  max-width: 100%;
}

.tab-panel {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.panel-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 4px 0;
}

.panel-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
}

.panel-actions {
  flex-shrink: 0;
}

.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #1a73e8;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.create-btn:hover {
  background: #1557b0;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 48px 24px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  color: #5f6368;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 20px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 8px 0;
}

.empty-subtitle {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
}

/* 智能体列表 */
.agent-cards {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
  gap: 16px !important;
  width: 100% !important;
}

.agent-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: all 0.2s ease;
  height: fit-content;
  box-sizing: border-box;
  min-height: 180px;
}

.agent-card:hover {
  border-color: #dadce0;
  box-shadow: 0 1px 6px rgba(32, 33, 36, 0.08);
}

.agent-info {
  flex: 1;
}

.agent-header {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  margin-bottom: 14px;
}

.agent-avatar {
  font-size: 24px;
  flex-shrink: 0;
}

.agent-basic {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.agent-description {
  font-size: 14px;
  color: #5f6368;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.agent-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
}

.meta-item {
  font-size: 13px;
  color: #70757a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item.status {
  font-weight: 500;
}

.meta-item.status.pending_review {
  color: #f57c00;
}

.meta-item.status.approved {
  color: #1e8e3e;
}

.meta-item.status.rejected {
  color: #d93025;
}

.meta-item.status.draft {
  color: #70757a;
}

.agent-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  align-items: center;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.action-btn.approve {
  background: #e8f5e8;
  color: #1e8e3e;
  border: 1px solid #e8f5e8;
}

.action-btn.approve:hover {
  background: #d4edda;
  border-color: #c3e6cb;
}

.action-btn.reject {
  background: #fce8e6;
  color: #d93025;
  border: 1px solid #fce8e6;
}

.action-btn.reject:hover {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 70px;
}

.status-badge.approved {
  background: #e8f5e8;
  color: #1e8e3e;
}

.status-badge.rejected {
  background: #fce8e6;
  color: #d93025;
}

.status-badge.draft {
  background: #f1f3f4;
  color: #70757a;
}

/* 用户表格 */
.table-container {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #3c4043;
  font-size: 14px;
}

.data-table td {
  color: #202124;
  font-size: 14px;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #1a73e8;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.user-name {
  font-weight: 500;
}

.role-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge.admin {
  background: #e3f2fd;
  color: #1976d2;
}

.role-badge.user {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #e8f5e8;
  color: #1e8e3e;
}

.status-badge.banned {
  background: #fce8e6;
  color: #d93025;
}

.status-badge.approved {
  background: #e8f5e8;
  color: #1e8e3e;
}

.status-badge.rejected {
  background: #fce8e6;
  color: #d93025;
}

.status-badge.draft {
  background: #fff3e0;
  color: #f57c00;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 交易记录 */
.transaction-items {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: #e8eaed;
  border-radius: 8px;
  overflow: hidden;
}

.transaction-item {
  background: #ffffff;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
}

.transaction-meta {
  font-size: 12px;
  color: #5f6368;
  display: flex;
  gap: 16px;
}

.transaction-amount {
  font-size: 14px;
  font-weight: 500;
  color: #1a73e8;
}

/* 模型卡片 */
.model-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.model-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin: 0 0 4px 0;
}

.model-api {
  font-size: 14px;
  color: #5f6368;
  margin: 0 0 12px 0;
  font-family: monospace;
}

.model-costs {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cost-item {
  font-size: 12px;
  color: #9aa0a6;
}

.model-actions {
  display: flex;
  gap: 8px;
}

/* 操作按钮 */
.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.action-btn.approve {
  background: #e8f5e8;
  color: #1e8e3e;
  border-color: #81c784;
}

.action-btn.approve:hover {
  background: #c8e6c9;
}

.action-btn.reject {
  background: #fce8e6;
  color: #d93025;
  border-color: #e57373;
}

.action-btn.reject:hover {
  background: #ffcdd2;
}

.action-btn.edit {
  background: #fff3e0;
  color: #f57c00;
  border-color: #ffb74d;
}

.action-btn.edit:hover {
  background: #ffe0b2;
}

.action-btn.delete {
  background: #fce8e6;
  color: #d93025;
  border-color: #e57373;
}

.action-btn.delete:hover {
  background: #ffcdd2;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 24px;
}

.modal-content {
  background: #ffffff;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e8eaed;
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: #202124;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: #9aa0a6;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  color: #5f6368;
  background: #f8f9fa;
}

.modal-form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
}

.form-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.cancel-btn,
.submit-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #ffffff;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.cancel-btn:hover {
  background: #f8f9fa;
}

.submit-btn {
  background: #1a73e8;
  color: #ffffff;
  border: none;
}

.submit-btn:hover:not(:disabled) {
  background: #1557b0;
}

.submit-btn:disabled {
  background: #f8f9fa;
  color: #9aa0a6;
  cursor: not-allowed;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-title {
    font-size: 24px;
  }

  .tab-nav {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-nav::-webkit-scrollbar {
    display: none;
  }

  .tab-btn {
    white-space: nowrap;
    margin-right: 24px;
  }

  .agent-cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
    gap: 14px !important;
  }

  .agent-card {
    padding: 16px;
    gap: 12px;
    min-height: 160px;
  }

  .agent-header {
    gap: 12px;
    margin-bottom: 12px;
  }

  .agent-avatar {
    font-size: 20px;
  }

  .agent-name {
    font-size: 15px;
  }

  .agent-description {
    font-size: 13px;
  }

  .agent-meta {
    gap: 4px;
  }

  .meta-item {
    font-size: 12px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 60px;
  }

  .status-badge {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 60px;
  }

  .data-table {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }

  .transaction-meta {
    flex-direction: column;
    gap: 4px;
  }

  .model-cards {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .panel-header {
    flex-direction: column;
    align-items: stretch;
  }

  .agent-cards {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .agent-card {
    padding: 14px;
    gap: 10px;
    min-height: 140px;
  }

  .agent-header {
    gap: 10px;
    margin-bottom: 10px;
  }

  .agent-meta {
    gap: 3px;
  }

  .agent-actions {
    flex-direction: column;
    gap: 6px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 11px;
    min-width: 100%;
  }

  .status-badge {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 100%;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    min-width: 600px;
  }
}
</style>