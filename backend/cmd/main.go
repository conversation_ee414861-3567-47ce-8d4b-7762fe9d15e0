package main

import (
	"log"
	"net/http"
	"os"
	"strings"

	"ai-agent-market/internal/api"
	"ai-agent-market/internal/config"
	"ai-agent-market/internal/database"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Initialize(cfg)
	if err != nil {
		log.Fatal("数据库初始化失败:", err)
	}

	// 设置Gin模式
	gin.SetMode(gin.DebugMode)

	// 创建路由器
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 动态配置CORS
	corsConfig := cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		AllowCredentials: true,
	}

	// 从环境变量获取允许的源
	if allowOrigins := os.Getenv("CORS_ALLOW_ORIGINS"); allowOrigins != "" {
		corsConfig.AllowOrigins = strings.Split(allowOrigins, ",")
	} else {
		// 默认允许本地和局域网访问
		corsConfig.AllowOriginFunc = func(origin string) bool {
			// 允许所有localhost和127.0.0.1
			if strings.Contains(origin, "localhost") || strings.Contains(origin, "127.0.0.1") {
				return true
			}
			// 允许局域网IP (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
			if strings.Contains(origin, "192.168.") ||
				strings.Contains(origin, "10.") ||
				(strings.Contains(origin, "172.") &&
					(strings.Contains(origin, "172.16.") || strings.Contains(origin, "172.17.") ||
						strings.Contains(origin, "172.18.") || strings.Contains(origin, "172.19.") ||
						strings.Contains(origin, "172.20.") || strings.Contains(origin, "172.21.") ||
						strings.Contains(origin, "172.22.") || strings.Contains(origin, "172.23.") ||
						strings.Contains(origin, "172.24.") || strings.Contains(origin, "172.25.") ||
						strings.Contains(origin, "172.26.") || strings.Contains(origin, "172.27.") ||
						strings.Contains(origin, "172.28.") || strings.Contains(origin, "172.29.") ||
						strings.Contains(origin, "172.30.") || strings.Contains(origin, "172.31."))) {
				return true
			}
			return false
		}
	}

	r.Use(cors.New(corsConfig))

	// 设置API路由
	api.SetupRoutes(r, db)

	// 启动服务器
	log.Printf("服务器启动在端口 %s", cfg.Port)
	log.Printf("允许CORS访问来源: 本地和局域网IP")
	if err := http.ListenAndServe(":"+cfg.Port, r); err != nil {
		log.Fatal("服务器启动失败:", err)
	}
}
