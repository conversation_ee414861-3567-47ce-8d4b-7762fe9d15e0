package models

import (
	"time"

	"gorm.io/gorm"
)

// 用户模型
type User struct {
	ID            uint           `json:"id" gorm:"primarykey"`
	Email         string         `json:"email" gorm:"unique;not null"`
	Password      string         `json:"-" gorm:"not null"`
	Nickname      *string        `json:"nickname"`
	ContactInfo   *string        `json:"contactInfo"`
	TokenBalance  int            `json:"tokenBalance" gorm:"default:0"`
	AffiliateCode string         `json:"affiliateCode" gorm:"unique;not null"`
	ReferredBy    *string        `json:"referredBy"`
	IsBanned      bool           `json:"isBanned" gorm:"default:false"`
	CreatedAt     time.Time      `json:"createdAt"`
	UpdatedAt     time.Time      `json:"updatedAt"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Agents        []Agent        `json:"agents,omitempty" gorm:"foreignKey:CreatorID"`
	Transactions  []Transaction  `json:"transactions,omitempty" gorm:"foreignKey:UserID"`
	Conversations []Conversation `json:"conversations,omitempty" gorm:"foreignKey:UserID"`
}

// 基础模型
type BaseModel struct {
	ID            uint      `json:"id" gorm:"primarykey"`
	Name          string    `json:"name" gorm:"not null"`
	URL           string    `json:"url" gorm:"not null"`
	APIKey        string    `json:"apiKey" gorm:"not null"`
	ModelName     string    `json:"modelName" gorm:"not null"`
	InputCost     float64   `json:"inputCost" gorm:"not null"`
	OutputCost    float64   `json:"outputCost" gorm:"not null"`
	ContextWindow *int      `json:"contextWindow"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`

	// 关联
	Agents []Agent `json:"agents,omitempty" gorm:"foreignKey:ModelID"`
}

// 智能体状态枚举
type AgentStatus string

const (
	AgentStatusDraft         AgentStatus = "draft"
	AgentStatusPendingReview AgentStatus = "pending_review"
	AgentStatusApproved      AgentStatus = "approved"
	AgentStatusRejected      AgentStatus = "rejected"
)

// 智能体模型
type Agent struct {
	ID           uint           `json:"id" gorm:"primarykey"`
	Name         string         `json:"name" gorm:"not null"`
	Description  string         `json:"description"`
	Icon         string         `json:"icon"`
	CreatorID    uint           `json:"creatorId" gorm:"not null"`
	IsPublic     bool           `json:"isPublic" gorm:"default:false"`
	ModelID      uint           `json:"modelId" gorm:"not null"`
	ProfitMargin float64        `json:"profitMargin" gorm:"default:0"`
	UsageCount   int            `json:"usageCount" gorm:"default:0"`
	Status       AgentStatus    `json:"status" gorm:"default:draft"`
	IsCustom     bool           `json:"isCustom" gorm:"default:false"`
	CreatedAt    time.Time      `json:"createdAt"`
	UpdatedAt    time.Time      `json:"updatedAt"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// Bot配置
	Persona         string `json:"persona"`
	UseGoogleSearch bool   `json:"useGoogleSearch" gorm:"default:false"`
	Knowledge       string `json:"knowledge"`

	// 联盟配置
	AffiliateEnabled        bool    `json:"affiliateEnabled" gorm:"default:false"`
	AffiliateCommissionRate float64 `json:"affiliateCommissionRate" gorm:"default:0"`

	// 关联
	Creator      User          `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
	Model        BaseModel     `json:"model,omitempty" gorm:"foreignKey:ModelID"`
	Transactions []Transaction `json:"transactions,omitempty" gorm:"foreignKey:AgentID"`
}

// 消息角色枚举
type MessageRole string

const (
	RoleUser   MessageRole = "user"
	RoleBot    MessageRole = "bot"
	RoleSystem MessageRole = "system"
)

// 消息模型
type Message struct {
	ID             uint        `json:"id" gorm:"primarykey"`
	ConversationID uint        `json:"conversationId" gorm:"not null"`
	Role           MessageRole `json:"role" gorm:"not null"`
	Content        string      `json:"content" gorm:"type:text"`
	Error          *string     `json:"error"`
	CreatedAt      time.Time   `json:"createdAt"`

	// 关联
	Conversation Conversation `json:"conversation,omitempty" gorm:"foreignKey:ConversationID"`
}

// 对话模型
type Conversation struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	Title     string         `json:"title" gorm:"not null"`
	UserID    uint           `json:"userId" gorm:"not null"`
	AgentID   uint           `json:"agentId" gorm:"not null"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Agent    Agent     `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	Messages []Message `json:"messages,omitempty" gorm:"foreignKey:ConversationID"`
}

// 交易类型枚举
type TransactionType string

const (
	TransactionTypeCreation  TransactionType = "creation"
	TransactionTypeAffiliate TransactionType = "affiliate"
)

// 交易模型
type Transaction struct {
	ID           uint            `json:"id" gorm:"primarykey"`
	AgentID      uint            `json:"agentId" gorm:"not null"`
	AgentName    string          `json:"agentName" gorm:"not null"`
	UserID       uint            `json:"userId" gorm:"not null"`
	TokenAmount  int             `json:"tokenAmount" gorm:"not null"`
	Type         TransactionType `json:"type" gorm:"not null"`
	EndUserEmail *string         `json:"endUserEmail"`
	CreatedAt    time.Time       `json:"createdAt"`

	// 关联
	Agent Agent `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	User  User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
}
