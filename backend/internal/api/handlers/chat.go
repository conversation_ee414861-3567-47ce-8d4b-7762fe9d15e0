package handlers

import (
	"net/http"
	"strconv"

	"ai-agent-market/internal/models"
	"ai-agent-market/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ChatHandler struct {
	db          *gorm.DB
	chatService *services.ChatService
}

func NewChatHandler(db *gorm.DB) *ChatHandler {
	return &ChatHandler{
		db:          db,
		chatService: services.NewChatService(db),
	}
}

type CreateConversationRequest struct {
	AgentID uint   `json:"agentId" binding:"required"`
	Title   string `json:"title"`
}

type SendMessageRequest struct {
	Content string `json:"content" binding:"required"`
}

// 获取特定智能体的对话列表
func (h *ChatHandler) GetConversationsByAgent(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("agentId")
	agentID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var conversations []models.Conversation
	if err := h.db.Where("user_id = ? AND agent_id = ?", userID, agentID).
		Preload("Agent").
		Order("updated_at DESC").
		Find(&conversations).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话列表失败"})
		return
	}

	c.JSON(http.StatusOK, conversations)
}

// 创建对话
func (h *ChatHandler) CreateConversation(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req CreateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证智能体是否存在
	var agent models.Agent
	if err := h.db.First(&agent, req.AgentID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	// 检查智能体状态
	if agent.Status != models.AgentStatusApproved {
		// 如果智能体未获批准，检查当前用户是否是所有者
		if agent.CreatorID != userID.(uint) {
			// 如果不是所有者，且智能体未批准，则返回错误
			switch agent.Status {
			case models.AgentStatusPendingReview:
				c.JSON(http.StatusForbidden, gin.H{"error": "智能体正在审核中，暂不可用"})
			case models.AgentStatusRejected:
				c.JSON(http.StatusForbidden, gin.H{"error": "智能体未通过审核，不可用"})
			default:
				c.JSON(http.StatusForbidden, gin.H{"error": "智能体当前不可用"})
			}
			return
		}
		// 如果是所有者，则允许继续操作，即使智能体未获批准
	}

	title := req.Title
	if title == "" {
		title = "与 " + agent.Name + " 的对话"
	}

	conversation := models.Conversation{
		Title:   title,
		UserID:  userID.(uint),
		AgentID: req.AgentID,
	}

	if err := h.db.Create(&conversation).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建对话失败"})
		return
	}

	// 预加载关联数据
	h.db.Preload("Agent").Preload("Messages").First(&conversation, conversation.ID)

	c.JSON(http.StatusCreated, conversation)
}

// 获取对话详情
func (h *ChatHandler) GetConversation(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	conversationID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的对话ID"})
		return
	}

	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", conversationID, userID).
		Preload("Agent").
		Preload("Messages", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at ASC")
		}).
		First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "对话不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		}
		return
	}

	c.JSON(http.StatusOK, conversation)
}

// 发送消息
func (h *ChatHandler) SendMessage(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	conversationID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的对话ID"})
		return
	}

	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证对话是否存在且属于当前用户
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", conversationID, userID).
		Preload("Agent.Model").
		Preload("Messages", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at ASC")
		}).
		First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "对话不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		}
		return
	}

	// 获取用户信息（检查代币余额）
	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败"})
		return
	}

	// 使用聊天服务处理消息
	_, botMessage, _, err := h.chatService.ProcessMessage(
		uint(conversationID),
		req.Content,
		&conversation,
		&user,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 只返回机器人的回复消息
	c.JSON(http.StatusOK, botMessage)
}

// 获取对话消息列表
func (h *ChatHandler) GetMessages(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	conversationID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的对话ID"})
		return
	}

	// 验证对话是否存在且属于当前用户
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", conversationID, userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "对话不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		}
		return
	}

	// 获取消息列表
	var messages []models.Message
	if err := h.db.Where("conversation_id = ?", conversationID).
		Order("created_at ASC").
		Find(&messages).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取消息列表失败"})
		return
	}

	c.JSON(http.StatusOK, messages)
}

// 清空对话消息
func (h *ChatHandler) ClearMessages(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	conversationID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的对话ID"})
		return
	}

	// 验证对话是否存在且属于当前用户
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", conversationID, userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "对话不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		}
		return
	}

	// 删除所有消息
	if err := h.db.Where("conversation_id = ?", conversationID).Delete(&models.Message{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清空消息失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "消息清空成功"})
}

// 删除对话
func (h *ChatHandler) DeleteConversation(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	conversationID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的对话ID"})
		return
	}

	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", conversationID, userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "对话不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取对话失败"})
		}
		return
	}

	// 删除对话（级联删除消息）
	if err := h.db.Delete(&conversation).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除对话失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "对话删除成功"})
}
