package handlers

import (
	"net/http"
	"strconv"

	"ai-agent-market/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AgentHandler struct {
	db *gorm.DB
}

func NewAgentHandler(db *gorm.DB) *AgentHandler {
	return &AgentHandler{db: db}
}

type CreateAgentRequest struct {
	Name                    string  `json:"name" binding:"required"`
	Description             string  `json:"description"`
	Icon                    string  `json:"icon"`
	ModelID                 uint    `json:"modelId" binding:"required"`
	ProfitMargin            float64 `json:"profitMargin"`
	Persona                 string  `json:"persona"`
	UseGoogleSearch         bool    `json:"useGoogleSearch"`
	Knowledge               string  `json:"knowledge"`
	AffiliateEnabled        bool    `json:"affiliateEnabled"`
	AffiliateCommissionRate float64 `json:"affiliateCommissionRate"`
}

// 获取公开的智能体列表
func (h *AgentHandler) GetPublicAgents(c *gin.Context) {
	var agents []models.Agent
	if err := h.db.Where("is_public = ? AND status = ?", true, models.AgentStatusApproved).
		Preload("Creator").
		Preload("Model").
		Find(&agents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体列表失败"})
		return
	}

	c.JSON(http.StatusOK, agents)
}

// 获取单个智能体信息
func (h *AgentHandler) GetAgent(c *gin.Context) {
	id := c.Param("id")
	agentID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ?", agentID).
		Preload("Creator").
		Preload("Model").
		First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	c.JSON(http.StatusOK, agent)
}

// 获取用户的智能体列表
func (h *AgentHandler) GetUserAgents(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var agents []models.Agent
	if err := h.db.Where("creator_id = ?", userID).
		Preload("Model").
		Order("created_at DESC").
		Find(&agents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体列表失败"})
		return
	}

	c.JSON(http.StatusOK, agents)
}

// 创建智能体
func (h *AgentHandler) CreateAgent(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req CreateAgentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证模型是否存在
	var model models.BaseModel
	if err := h.db.First(&model, req.ModelID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "指定的模型不存在"})
		return
	}

	agent := models.Agent{
		Name:                    req.Name,
		Description:             req.Description,
		Icon:                    req.Icon,
		CreatorID:               userID.(uint),
		ModelID:                 req.ModelID,
		ProfitMargin:            req.ProfitMargin,
		Persona:                 req.Persona,
		UseGoogleSearch:         req.UseGoogleSearch,
		Knowledge:               req.Knowledge,
		AffiliateEnabled:        req.AffiliateEnabled,
		AffiliateCommissionRate: req.AffiliateCommissionRate,
		Status:                  models.AgentStatusDraft,
		IsPublic:                false,
	}

	if err := h.db.Create(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建智能体失败"})
		return
	}

	c.JSON(http.StatusCreated, agent)
}

// 更新智能体
func (h *AgentHandler) UpdateAgent(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	agentID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ? AND creator_id = ?", agentID, userID).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	var req CreateAgentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果智能体已经发布，重置状态为草稿
	if agent.Status == models.AgentStatusApproved {
		agent.Status = models.AgentStatusDraft
		agent.IsPublic = false
	}

	// 更新字段
	agent.Name = req.Name
	agent.Description = req.Description
	agent.Icon = req.Icon
	agent.ModelID = req.ModelID
	agent.ProfitMargin = req.ProfitMargin
	agent.Persona = req.Persona
	agent.UseGoogleSearch = req.UseGoogleSearch
	agent.Knowledge = req.Knowledge
	agent.AffiliateEnabled = req.AffiliateEnabled
	agent.AffiliateCommissionRate = req.AffiliateCommissionRate

	if err := h.db.Save(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新智能体失败"})
		return
	}

	c.JSON(http.StatusOK, agent)
}

// 删除智能体
func (h *AgentHandler) DeleteAgent(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	agentID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ? AND creator_id = ?", agentID, userID).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	if err := h.db.Delete(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除智能体失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "智能体删除成功"})
}

// 发布智能体（提交审核）
func (h *AgentHandler) PublishAgent(c *gin.Context) {
	userID, _ := c.Get("user_id")
	id := c.Param("id")
	agentID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ? AND creator_id = ?", agentID, userID).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在或没有权限"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	if agent.Status != models.AgentStatusDraft && agent.Status != models.AgentStatusRejected {
		c.JSON(http.StatusBadRequest, gin.H{"error": "只能发布草稿状态或被拒绝的智能体"})
		return
	}

	agent.Status = models.AgentStatusPendingReview
	if err := h.db.Save(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "提交审核失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "智能体已提交审核"})
}

// 获取所有模型
func (h *AgentHandler) GetModels(c *gin.Context) {
	var models []models.BaseModel
	if err := h.db.Find(&models).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取模型列表失败"})
		return
	}

	c.JSON(http.StatusOK, models)
}
