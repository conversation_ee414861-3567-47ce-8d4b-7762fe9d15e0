package handlers

import (
	"fmt"
	"net/http"

	"ai-agent-market/internal/models"
	"ai-agent-market/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminHandler struct {
	db *gorm.DB
}

func NewAdminHandler(db *gorm.DB) *AdminHandler {
	return &AdminHandler{db: db}
}

// 获取用户列表
func (h *AdminHandler) GetUsers(c *gin.Context) {
	page, _ := utils.ParseInt(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	limit, _ := utils.ParseInt(c<PERSON>("limit", "20"))
	offset := (page - 1) * limit

	var users []models.User
	var total int64

	h.db.Model(&models.User{}).Count(&total)

	if err := h.db.Select("id, email, nickname, token_balance, affiliate_code, referred_by, is_banned, created_at").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&users).Error; err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败"})
		return
	}

	// 为每个用户添加角色信息
	type UserWithRole struct {
		models.User
		Role string `json:"role"`
	}

	var usersWithRole []UserWithRole
	for _, user := range users {
		role := "user"
		if user.Email == "<EMAIL>" {
			role = "admin"
		}

		usersWithRole = append(usersWithRole, UserWithRole{
			User: user,
			Role: role,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"users": usersWithRole,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// 禁用用户
func (h *AdminHandler) BanUser(c *gin.Context) {
	userID, err := utils.ParseID(c, "id")
	if err != nil {
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		if !utils.CheckRecordExists(err, c, "用户不存在") {
			return
		}
	}

	user.IsBanned = true
	if err := h.db.Save(&user).Error; err != nil {
		utils.ErrorJSON(c, http.StatusInternalServerError, "禁用用户失败")
		return
	}

	utils.SuccessJSON(c, "用户已被禁用")
}

// 解禁用户
func (h *AdminHandler) UnbanUser(c *gin.Context) {
	id := c.Param("id")
	userID, err := utils.ParseUint(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户失败"})
		}
		return
	}

	user.IsBanned = false
	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解禁用户失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户已被解禁"})
}

// 获取所有智能体
func (h *AdminHandler) GetAllAgents(c *gin.Context) {
	var agents []models.Agent
	if err := h.db.Preload("Creator").
		Preload("Model").
		Order("created_at DESC").
		Find(&agents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		return
	}

	c.JSON(http.StatusOK, agents)
}

// 获取待审核的智能体
func (h *AdminHandler) GetPendingAgents(c *gin.Context) {
	var agents []models.Agent
	if err := h.db.Where("status = ?", models.AgentStatusPendingReview).
		Preload("Creator").
		Preload("Model").
		Order("created_at ASC").
		Find(&agents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取待审核智能体失败"})
		return
	}

	c.JSON(http.StatusOK, agents)
}

// 批准智能体
func (h *AdminHandler) ApproveAgent(c *gin.Context) {
	id := c.Param("id")
	agentID, err := utils.ParseUint(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var agent models.Agent
	if err := h.db.First(&agent, agentID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	if agent.Status != models.AgentStatusPendingReview {
		c.JSON(http.StatusBadRequest, gin.H{"error": "只能批准待审核状态的智能体"})
		return
	}

	agent.Status = models.AgentStatusApproved
	agent.IsPublic = true
	if err := h.db.Save(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批准智能体失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "智能体已批准"})
}

// 拒绝智能体
func (h *AdminHandler) RejectAgent(c *gin.Context) {
	id := c.Param("id")
	agentID, err := utils.ParseUint(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的智能体ID"})
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var agent models.Agent
	if err := h.db.First(&agent, agentID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "智能体不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取智能体失败"})
		}
		return
	}

	if agent.Status != models.AgentStatusPendingReview {
		c.JSON(http.StatusBadRequest, gin.H{"error": "只能拒绝待审核状态的智能体"})
		return
	}

	agent.Status = models.AgentStatusRejected
	agent.IsPublic = false
	if err := h.db.Save(&agent).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "拒绝智能体失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "智能体已拒绝", "reason": req.Reason})
}

// 获取所有交易记录
func (h *AdminHandler) GetAllTransactions(c *gin.Context) {
	page, _ := utils.ParseInt(c.DefaultQuery("page", "1"))
	limit, _ := utils.ParseInt(c.DefaultQuery("limit", "50"))
	offset := (page - 1) * limit

	var transactions []models.Transaction
	var total int64

	h.db.Model(&models.Transaction{}).Count(&total)

	if err := h.db.Preload("User").
		Preload("Agent").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取交易记录失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"total":        total,
		"page":         page,
		"limit":        limit,
	})
}

// 模型管理相关请求结构
type CreateModelRequest struct {
	Name          string  `json:"name" binding:"required"`
	URL           string  `json:"url" binding:"required"`
	APIKey        string  `json:"apiKey" binding:"required"`
	ModelName     string  `json:"modelName" binding:"required"`
	InputCost     float64 `json:"inputCost" binding:"required"`
	OutputCost    float64 `json:"outputCost" binding:"required"`
	ContextWindow *int    `json:"contextWindow"`
}

type UpdateModelRequest struct {
	Name          string  `json:"name"`
	URL           string  `json:"url"`
	APIKey        string  `json:"apiKey"`
	ModelName     string  `json:"modelName"`
	InputCost     float64 `json:"inputCost"`
	OutputCost    float64 `json:"outputCost"`
	ContextWindow *int    `json:"contextWindow"`
}

// 获取所有模型
func (h *AdminHandler) GetAllModels(c *gin.Context) {
	var models []models.BaseModel
	if err := h.db.Find(&models).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取模型列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"models": models,
		"total":  len(models),
	})
}

// 创建新模型
func (h *AdminHandler) CreateModel(c *gin.Context) {
	var req CreateModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查模型名称是否已存在
	var existingModel models.BaseModel
	if err := h.db.Where("name = ?", req.Name).First(&existingModel).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "模型名称已存在"})
		return
	}

	model := models.BaseModel{
		Name:          req.Name,
		URL:           req.URL,
		APIKey:        req.APIKey,
		ModelName:     req.ModelName,
		InputCost:     req.InputCost,
		OutputCost:    req.OutputCost,
		ContextWindow: req.ContextWindow,
	}

	if err := h.db.Create(&model).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建模型失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "模型创建成功",
		"model":   model,
	})
}

// 更新模型
func (h *AdminHandler) UpdateModel(c *gin.Context) {
	id := c.Param("id")
	modelID, err := utils.ParseUint(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的模型ID"})
		return
	}

	var model models.BaseModel
	if err := h.db.First(&model, modelID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "模型不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "查找模型失败"})
		}
		return
	}

	var req UpdateModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查名称是否与其他模型冲突
	if req.Name != "" && req.Name != model.Name {
		var existingModel models.BaseModel
		if err := h.db.Where("name = ? AND id != ?", req.Name, modelID).First(&existingModel).Error; err == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "模型名称已存在"})
			return
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.URL != "" {
		updates["url"] = req.URL
	}
	if req.APIKey != "" {
		updates["api_key"] = req.APIKey
	}
	if req.ModelName != "" {
		updates["model_name"] = req.ModelName
	}
	if req.InputCost > 0 {
		updates["input_cost"] = req.InputCost
	}
	if req.OutputCost > 0 {
		updates["output_cost"] = req.OutputCost
	}
	if req.ContextWindow != nil {
		updates["context_window"] = req.ContextWindow
	}

	if err := h.db.Model(&model).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新模型失败"})
		return
	}

	// 重新加载模型数据
	if err := h.db.First(&model, modelID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "重新加载模型数据失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "模型更新成功",
		"model":   model,
	})
}

// 删除模型
func (h *AdminHandler) DeleteModel(c *gin.Context) {
	id := c.Param("id")
	modelID, err := utils.ParseUint(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的模型ID"})
		return
	}

	var model models.BaseModel
	if err := h.db.First(&model, modelID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "模型不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "查找模型失败"})
		}
		return
	}

	// 检查是否有智能体正在使用此模型
	var agentCount int64
	if err := h.db.Model(&models.Agent{}).Where("model_id = ?", modelID).Count(&agentCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "检查模型使用情况失败"})
		return
	}

	if agentCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("无法删除模型，还有 %d 个智能体正在使用此模型", agentCount),
		})
		return
	}

	if err := h.db.Delete(&model).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除模型失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "模型删除成功",
	})
}
