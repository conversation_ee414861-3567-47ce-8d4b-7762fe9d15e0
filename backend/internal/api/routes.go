package api

import (
	"ai-agent-market/internal/api/handlers"
	"ai-agent-market/internal/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	// 创建处理器实例
	userHandler := handlers.NewUserHandler(db)
	agentHandler := handlers.NewAgentHandler(db)
	chatHandler := handlers.NewChatHandler(db)
	adminHandler := handlers.NewAdminHandler(db)

	// API v1 路由组
	v1 := router.Group("/api/v1")

	// 公开路由（无需认证）
	auth := v1.Group("/auth")
	{
		auth.POST("/login", userHandler.Login)
		auth.POST("/register", userHandler.Register)
		auth.POST("/refresh", userHandler.RefreshToken)
	}

	// 公开的智能体路由
	public := v1.Group("/public")
	{
		public.GET("/agents", agentHandler.GetPublicAgents)
		public.GET("/agents/:id", agentHandler.GetAgent)
		public.GET("/models", agentHandler.GetModels)
	}

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(middleware.AuthMiddleware())
	{
		// 用户相关
		user := protected.Group("/user")
		{
			user.GET("/profile", userHandler.GetProfile)
			user.PUT("/profile", userHandler.UpdateProfile)
			user.POST("/change-password", userHandler.ChangePassword)
			user.GET("/transactions", userHandler.GetTransactions)
			user.GET("/conversations", userHandler.GetConversations)
		}

		// 智能体相关
		agents := protected.Group("/agents")
		{
			agents.GET("/", agentHandler.GetUserAgents)
			agents.GET("/:id", agentHandler.GetAgent)
			agents.POST("/", agentHandler.CreateAgent)
			agents.PUT("/:id", agentHandler.UpdateAgent)
			agents.DELETE("/:id", agentHandler.DeleteAgent)
			agents.POST("/:id/publish", agentHandler.PublishAgent)
		}

		// 聊天相关
		chat := protected.Group("/chat")
		{
			// 对话管理
			chat.POST("/conversations", chatHandler.CreateConversation)
			chat.GET("/conversations/:id", chatHandler.GetConversation)
			chat.GET("/conversations/:id/messages", chatHandler.GetMessages)
			chat.POST("/conversations/:id/messages", chatHandler.SendMessage)
			chat.DELETE("/conversations/:id/messages", chatHandler.ClearMessages)
			chat.DELETE("/conversations/:id", chatHandler.DeleteConversation)

			// 根据智能体获取对话
			chat.GET("/conversations/agent/:agentId", chatHandler.GetConversationsByAgent)
		}

		// 管理员路由
		admin := protected.Group("/admin")
		admin.Use(middleware.AdminMiddleware())
		{
			admin.GET("/users", adminHandler.GetUsers)
			admin.PUT("/users/:id/ban", adminHandler.BanUser)
			admin.PUT("/users/:id/unban", adminHandler.UnbanUser)
			admin.GET("/agents", adminHandler.GetAllAgents)
			admin.GET("/agents/pending", adminHandler.GetPendingAgents)
			admin.PUT("/agents/:id/approve", adminHandler.ApproveAgent)
			admin.PUT("/agents/:id/reject", adminHandler.RejectAgent)
			admin.GET("/transactions", adminHandler.GetAllTransactions)

			// 模型管理
			admin.GET("/models", adminHandler.GetAllModels)
			admin.POST("/models", adminHandler.CreateModel)
			admin.PUT("/models/:id", adminHandler.UpdateModel)
			admin.DELETE("/models/:id", adminHandler.DeleteModel)
		}
	}
}
