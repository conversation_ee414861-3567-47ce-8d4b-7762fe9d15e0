package services

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"ai-agent-market/internal/models"

	"gorm.io/gorm"
)

type ChatService struct {
	db *gorm.DB
}

func NewChatService(db *gorm.DB) *ChatService {
	return &ChatService{db: db}
}

// OpenAI API请求结构
type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIRequest struct {
	Model    string          `json:"model"`
	Messages []OpenAIMessage `json:"messages"`
}

type OpenAIChoice struct {
	Message OpenAIMessage `json:"message"`
}

type OpenAIUsage struct {
	TotalTokens int `json:"total_tokens"`
}

type OpenAIResponse struct {
	Choices []OpenAIChoice `json:"choices"`
	Usage   OpenAIUsage    `json:"usage"`
}

// 处理聊天消息
func (s *ChatService) ProcessMessage(
	conversationID uint,
	content string,
	conversation *models.Conversation,
	user *models.User,
) (*models.Message, *models.Message, int, error) {

	// 1. 创建用户消息
	userMessage := &models.Message{
		ConversationID: conversationID,
		Role:           models.RoleUser,
		Content:        content,
	}

	if err := s.db.Create(userMessage).Error; err != nil {
		return nil, nil, 0, fmt.Errorf("保存用户消息失败: %v", err)
	}

	// 2. 构建对话历史
	messages := []OpenAIMessage{
		{
			Role:    "system",
			Content: conversation.Agent.Persona,
		},
	}

	// 添加知识库信息
	if conversation.Agent.Knowledge != "" {
		messages = append(messages, OpenAIMessage{
			Role:    "system",
			Content: "知识库信息: " + conversation.Agent.Knowledge,
		})
	}

	// 添加历史消息（最近10条）
	historyMessages := conversation.Messages
	startIndex := 0
	if len(historyMessages) > 10 {
		startIndex = len(historyMessages) - 10
	}

	for i := startIndex; i < len(historyMessages); i++ {
		msg := historyMessages[i]
		role := "user"
		if msg.Role == models.RoleBot {
			role = "assistant"
		}
		messages = append(messages, OpenAIMessage{
			Role:    role,
			Content: msg.Content,
		})
	}

	// 添加当前用户消息
	messages = append(messages, OpenAIMessage{
		Role:    "user",
		Content: content,
	})

	// 3. 调用AI模型
	aiResponse, tokensUsed, err := s.callAIModel(&conversation.Agent, messages)
	if err != nil {
		// 创建错误消息
		errMsg := err.Error()
		botMessage := &models.Message{
			ConversationID: conversationID,
			Role:           models.RoleBot,
			Content:        "抱歉，我现在无法回应。请稍后再试。",
			Error:          &errMsg,
		}
		s.db.Create(botMessage)
		return userMessage, botMessage, 0, fmt.Errorf("AI回复失败: %v", err)
	}

	// 4. 计算费用
	cost := s.calculateCost(&conversation.Agent, tokensUsed)

	// 检查用户代币余额
	if user.TokenBalance < cost {
		botMessage := &models.Message{
			ConversationID: conversationID,
			Role:           models.RoleBot,
			Content:        fmt.Sprintf("代币余额不足。本次对话需要 %d 代币，您当前余额: %d", cost, user.TokenBalance),
		}
		s.db.Create(botMessage)
		return userMessage, botMessage, 0, errors.New("代币余额不足")
	}

	// 5. 扣除用户代币
	user.TokenBalance -= cost
	if err := s.db.Save(user).Error; err != nil {
		return userMessage, nil, 0, fmt.Errorf("扣除代币失败: %v", err)
	}

	// 6. 创建机器人回复消息
	botMessage := &models.Message{
		ConversationID: conversationID,
		Role:           models.RoleBot,
		Content:        aiResponse,
	}

	if err := s.db.Create(botMessage).Error; err != nil {
		return userMessage, nil, 0, fmt.Errorf("保存机器人消息失败: %v", err)
	}

	// 7. 记录交易
	s.recordTransaction(conversation.Agent.ID, conversation.Agent.Name, user.ID, cost)

	// 8. 更新智能体使用次数
	s.db.Model(&conversation.Agent).Update("usage_count", gorm.Expr("usage_count + ?", 1))

	return userMessage, botMessage, tokensUsed, nil
}

// 调用AI模型
func (s *ChatService) callAIModel(agent *models.Agent, messages []OpenAIMessage) (string, int, error) {
	// 构建请求
	request := OpenAIRequest{
		Model:    agent.Model.ModelName,
		Messages: messages,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", 0, err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", agent.Model.URL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", 0, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+agent.Model.APIKey)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", 0, fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", 0, err
	}

	var response OpenAIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", 0, err
	}

	if len(response.Choices) == 0 {
		return "", 0, errors.New("AI模型没有返回回复")
	}

	return response.Choices[0].Message.Content, response.Usage.TotalTokens, nil
}

// 计算费用
func (s *ChatService) calculateCost(agent *models.Agent, tokensUsed int) int {
	// 简化计算：假设输入输出token比例为1:1
	inputTokens := tokensUsed / 2
	outputTokens := tokensUsed / 2

	// 计算基础成本（以美分为单位）
	baseCost := float64(inputTokens)*agent.Model.InputCost/1000 + float64(outputTokens)*agent.Model.OutputCost/1000

	// 添加创作者利润
	totalCost := baseCost * (1 + agent.ProfitMargin)

	// 转换为代币（1代币 = 0.01美元 = 1美分）
	return int(totalCost * 100)
}

// 记录交易
func (s *ChatService) recordTransaction(agentID uint, agentName string, userID uint, tokenAmount int) {
	// 记录创作者收益
	var agent models.Agent
	if err := s.db.Preload("Creator").First(&agent, agentID).Error; err != nil {
		return
	}

	// 计算创作者收益
	creatorProfit := int(float64(tokenAmount) * agent.ProfitMargin / (1 + agent.ProfitMargin))

	transaction := models.Transaction{
		AgentID:     agentID,
		AgentName:   agentName,
		UserID:      agent.CreatorID,
		TokenAmount: creatorProfit,
		Type:        models.TransactionTypeCreation,
	}

	s.db.Create(&transaction)

	// 给创作者添加代币
	s.db.Model(&agent.Creator).Update("token_balance", gorm.Expr("token_balance + ?", creatorProfit))

	// 如果启用了联盟营销，记录推荐人收益
	if agent.AffiliateEnabled && agent.Creator.ReferredBy != nil {
		var referrer models.User
		if err := s.db.Where("affiliate_code = ?", *agent.Creator.ReferredBy).First(&referrer).Error; err == nil {
			affiliateProfit := int(float64(creatorProfit) * agent.AffiliateCommissionRate)

			affiliateTransaction := models.Transaction{
				AgentID:     agentID,
				AgentName:   agentName,
				UserID:      referrer.ID,
				TokenAmount: affiliateProfit,
				Type:        models.TransactionTypeAffiliate,
			}

			s.db.Create(&affiliateTransaction)
			s.db.Model(&referrer).Update("token_balance", gorm.Expr("token_balance + ?", affiliateProfit))
		}
	}
}
