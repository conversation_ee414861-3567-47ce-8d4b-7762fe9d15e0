package database

import (
	"fmt"
	"strings"

	"ai-agent-market/internal/config"
	"ai-agent-market/internal/models"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Initialize(cfg *config.Config) (*gorm.DB, error) {
	// 首先尝试创建数据库
	if err := createDatabaseIfNotExists(cfg); err != nil {
		return nil, fmt.Errorf("创建数据库失败: %v", err)
	}

	// 连接到指定数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)

	// 自动迁移
	if err := db.AutoMigrate(
		&models.User{},
		&models.BaseModel{},
		&models.Agent{},
		&models.Conversation{},
		&models.Message{},
		&models.Transaction{},
	); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %v", err)
	}

	// 创建默认数据
	createDefaultModels(db)
	createDefaultAgents(db)

	return db, nil
}

// createDatabaseIfNotExists 检查并创建数据库
func createDatabaseIfNotExists(cfg *config.Config) error {
	// 解析数据库URL提取数据库名
	dbName := cfg.Database.Database

	// 构建不包含数据库名的连接字符串
	serverURL := fmt.Sprintf("%s:%s@tcp(%s:%d)/?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
	)

	// 连接到MySQL服务器（不指定数据库）
	db, err := gorm.Open(mysql.Open(serverURL), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("连接MySQL服务器失败: %v", err)
	}

	// 检查数据库是否存在
	var exists int64
	err = db.Raw("SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", dbName).Scan(&exists).Error
	if err != nil {
		return fmt.Errorf("检查数据库是否存在失败: %v", err)
	}

	// 如果数据库不存在，则创建它
	if exists == 0 {
		err = db.Exec(fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName)).Error
		if err != nil {
			return fmt.Errorf("创建数据库失败: %v", err)
		}
	}

	return nil
}

// extractDatabaseName 从数据库URL中提取数据库名
func extractDatabaseName(databaseURL string) (string, error) {
	// 示例URL: "root:123456@tcp(localhost:3306)/ai_agent_market?charset=utf8mb4&parseTime=True&loc=Local"
	// 提取数据库名: ai_agent_market

	// 找到最后一个'/'和'?'之间的内容
	lastSlashIndex := strings.LastIndex(databaseURL, "/")
	if lastSlashIndex == -1 {
		return "", fmt.Errorf("无效的数据库URL格式")
	}

	remainingURL := databaseURL[lastSlashIndex+1:]
	questionMarkIndex := strings.Index(remainingURL, "?")

	var dbName string
	if questionMarkIndex == -1 {
		dbName = remainingURL
	} else {
		dbName = remainingURL[:questionMarkIndex]
	}

	if dbName == "" {
		return "", fmt.Errorf("无法从URL中提取数据库名")
	}

	return dbName, nil
}

func createDefaultModels(db *gorm.DB) {
	defaultModels := []models.BaseModel{
		{
			Name:          "OpenAI GPT-4",
			URL:           "https://api.openai.com/v1/chat/completions",
			APIKey:        "your-openai-api-key",
			ModelName:     "gpt-4",
			InputCost:     0.03, // $0.03 per 1K tokens
			OutputCost:    0.06, // $0.06 per 1K tokens
			ContextWindow: func(i int) *int { return &i }(8192),
		},
		{
			Name:          "OpenAI GPT-3.5-Turbo",
			URL:           "https://api.openai.com/v1/chat/completions",
			APIKey:        "your-openai-api-key",
			ModelName:     "gpt-3.5-turbo",
			InputCost:     0.001, // $0.001 per 1K tokens
			OutputCost:    0.002, // $0.002 per 1K tokens
			ContextWindow: func(i int) *int { return &i }(4096),
		},
	}

	for _, model := range defaultModels {
		var existing models.BaseModel
		if err := db.Where("model_name = ?", model.ModelName).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				db.Create(&model)
			}
		}
	}
}

func createDefaultAgents(db *gorm.DB) {
	// 首先创建管理员用户
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		// 如果哈希失败，记录错误但继续执行
		fmt.Printf("密码哈希失败: %v\n", err)
		hashedPassword = []byte("admin123") // 降级使用明文密码
	}

	adminUser := models.User{
		Email:         "<EMAIL>",
		Password:      string(hashedPassword),
		AffiliateCode: "ADMIN",
		TokenBalance:  10000, // 管理员初始代币
	}

	var existingUser models.User
	if err := db.Where("email = ?", adminUser.Email).First(&existingUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			db.Create(&adminUser)
			fmt.Printf("管理员用户已创建: %s (密码: admin123)\n", adminUser.Email)
		}
	} else {
		adminUser = existingUser
	}

	// 创建系统用户用于系统智能体
	systemHashedPassword, err := bcrypt.GenerateFromPassword([]byte("system123"), bcrypt.DefaultCost)
	if err != nil {
		fmt.Printf("系统用户密码哈希失败: %v\n", err)
		systemHashedPassword = []byte("system123")
	}

	var systemUser models.User
	if err := db.Where("email = ?", "<EMAIL>").First(&systemUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新的系统用户
			newSystemUser := models.User{
				Email:         "<EMAIL>",
				Password:      string(systemHashedPassword),
				AffiliateCode: "SYSTEM",
				TokenBalance:  0,
			}
			if err := db.Create(&newSystemUser).Error; err != nil {
				fmt.Printf("系统用户创建失败: %v\n", err)
				return
			}
			systemUser = newSystemUser
			fmt.Printf("系统用户已创建: %s (密码: system123)\n", systemUser.Email)
		} else {
			fmt.Printf("查询系统用户失败: %v\n", err)
			return
		}
	}

	// 获取默认模型
	var gpt4Model models.BaseModel
	db.Where("model_name = ?", "gpt-4").First(&gpt4Model)

	defaultAgents := []models.Agent{
		{
			Name:            "通用助手",
			Description:     "一个乐于助人的全能AI助手，可以回答各种问题。",
			Icon:            "bot",
			CreatorID:       systemUser.ID,
			IsPublic:        true,
			ModelID:         gpt4Model.ID,
			ProfitMargin:    0.1,
			UsageCount:      138,
			Status:          models.AgentStatusApproved,
			Persona:         "你是一个乐于助人的人工智能助手。你的回答应该简洁、清晰且友好。",
			UseGoogleSearch: false,
			Knowledge:       "",
		},
		{
			Name:            "旅行规划师",
			Description:     "规划您的下一次冒险，提供目的地建议和行程安排。",
			Icon:            "travel",
			CreatorID:       systemUser.ID,
			IsPublic:        true,
			ModelID:         gpt4Model.ID,
			ProfitMargin:    1.0,
			UsageCount:      92,
			Status:          models.AgentStatusApproved,
			Persona:         "你是一位经验丰富的旅行规划师。利用你的知识为用户提供详细、吸引人的旅行建议。",
			UseGoogleSearch: false,
			Knowledge:       "当前季节是夏季，适合海滩和山地旅行。",
		},
		{
			Name:            "编程高手",
			Description:     "帮助您编写、调试代码，并提供关于最佳实践的建议。",
			Icon:            "code",
			CreatorID:       systemUser.ID,
			IsPublic:        true,
			ModelID:         gpt4Model.ID,
			ProfitMargin:    1.0,
			UsageCount:      250,
			Status:          models.AgentStatusApproved,
			Persona:         "你是一位世界级的软件工程师，精通多种编程语言。你的代码示例应该是清晰、高效且有良好注释的。",
			UseGoogleSearch: false,
			Knowledge:       "常见的设计模式包括：单例模式、工厂模式、观察者模式。在回答时，优先考虑代码的可读性和可维护性。",
		},
		{
			Name:            "创意写手",
			Description:     "创作故事、诗歌、剧本或任何您能想象到的创意文本。",
			Icon:            "write",
			CreatorID:       systemUser.ID,
			IsPublic:        true,
			ModelID:         gpt4Model.ID,
			ProfitMargin:    2.0,
			UsageCount:      77,
			Status:          models.AgentStatusApproved,
			Persona:         "你是一位富有想象力的创意写手。你的文字应该生动、有感染力，并能激发读者的情感。",
			UseGoogleSearch: false,
			Knowledge:       "经典的故事结构遵循\"英雄之旅\"模式，包括启程、考验和归来三个主要阶段。",
		},
	}

	for _, agent := range defaultAgents {
		var existing models.Agent
		if err := db.Where("name = ? AND creator_id = ?", agent.Name, systemUser.ID).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				db.Create(&agent)
			}
		}
	}
}
