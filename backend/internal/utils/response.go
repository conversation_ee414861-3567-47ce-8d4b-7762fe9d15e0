package utils

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 错误响应结构
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// 成功响应结构
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 统一的错误响应
func ErrorJSON(c *gin.Context, statusCode int, message string) {
	c.JSON(statusCode, ErrorResponse{Error: message})
}

// 统一的成功响应
func SuccessJSON(c *gin.Context, message string, data ...interface{}) {
	response := SuccessResponse{Message: message}
	if len(data) > 0 {
		response.Data = data[0]
	}
	c.JSON(http.StatusOK, response)
}

// 解析并验证ID参数
func ParseID(c *gin.Context, paramName string) (uint, error) {
	idStr := c.Param(paramName)
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ErrorJSON(c, http.StatusBadRequest, "无效的ID参数")
		return 0, err
	}
	return uint(id), nil
}

// 解析整数
func ParseInt(s string) (int, error) {
	return strconv.Atoi(s)
}

// 解析无符号整数
func ParseUint(s string) (uint, error) {
	id, err := strconv.ParseUint(s, 10, 32)
	return uint(id), err
}

// 检查记录是否存在
func CheckRecordExists(err error, c *gin.Context, notFoundMessage string) bool {
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorJSON(c, http.StatusNotFound, notFoundMessage)
		} else {
			ErrorJSON(c, http.StatusInternalServerError, "数据库查询失败")
		}
		return false
	}
	return true
}

// 绑定JSON数据
func BindJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		ErrorJSON(c, http.StatusBadRequest, "请求数据格式错误: "+err.Error())
		return false
	}
	return true
}
