-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_agent_market CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ai_agent_market;

-- 用户表
CREATE TABLE users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    contact_info TEXT,
    token_balance INT DEFAULT 0,
    affiliate_code VARCHAR(10) NOT NULL UNIQUE,
    referred_by VARCHAR(10),
    is_banned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_affiliate_code (affiliate_code),
    INDEX idx_referred_by (referred_by)
);

-- 基础模型表
CREATE TABLE base_models (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    api_key VARCHAR(500) NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    input_cost DECIMAL(10, 6) NOT NULL,
    output_cost DECIMAL(10, 6) NOT NULL,
    context_window INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 智能体表
CREATE TABLE agents (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    creator_id INT UNSIGNED NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    model_id INT UNSIGNED NOT NULL,
    profit_margin DECIMAL(8, 4) DEFAULT 0,
    usage_count INT DEFAULT 0,
    status ENUM('draft', 'pending_review', 'approved', 'rejected') DEFAULT 'draft',
    is_custom BOOLEAN DEFAULT FALSE,
    
    -- Bot配置
    persona TEXT,
    use_google_search BOOLEAN DEFAULT FALSE,
    knowledge TEXT,
    
    -- 联盟配置
    affiliate_enabled BOOLEAN DEFAULT FALSE,
    affiliate_commission_rate DECIMAL(8, 4) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES base_models(id) ON DELETE RESTRICT,
    INDEX idx_creator_id (creator_id),
    INDEX idx_model_id (model_id),
    INDEX idx_status (status),
    INDEX idx_is_public (is_public)
);

-- 对话表
CREATE TABLE conversations (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    agent_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_updated_at (updated_at)
);

-- 消息表
CREATE TABLE messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    conversation_id INT UNSIGNED NOT NULL,
    role ENUM('user', 'bot', 'system') NOT NULL,
    content TEXT NOT NULL,
    error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_created_at (created_at)
);

-- 交易表
CREATE TABLE transactions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    agent_id INT UNSIGNED NOT NULL,
    agent_name VARCHAR(255) NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    token_amount INT NOT NULL,
    type ENUM('creation', 'affiliate') NOT NULL,
    end_user_email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_agent_id (agent_id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);

-- 插入默认数据
INSERT INTO base_models (name, url, api_key, model_name, input_cost, output_cost, context_window) VALUES
('OpenAI GPT-4', 'https://api.openai.com/v1/chat/completions', 'your-openai-api-key', 'gpt-4', 0.03, 0.06, 8192),
('OpenAI GPT-3.5-Turbo', 'https://api.openai.com/v1/chat/completions', 'your-openai-api-key', 'gpt-3.5-turbo', 0.001, 0.002, 4096);

-- 插入系统用户
INSERT INTO users (email, password, affiliate_code, token_balance) VALUES
('<EMAIL>', '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36y4LRJ1DsGL/TA4xOhjRY2', 'SYSTEM', 0);

-- 插入默认智能体
INSERT INTO agents (name, description, icon, creator_id, is_public, model_id, profit_margin, usage_count, status, persona, use_google_search, knowledge) VALUES
('通用助手', '一个乐于助人的全能AI助手，可以回答各种问题。', 'bot', 1, TRUE, 1, 0.1, 138, 'approved', '你是一个乐于助人的人工智能助手。你的回答应该简洁、清晰且友好。', FALSE, ''),
('旅行规划师', '规划您的下一次冒险，提供目的地建议和行程安排。', 'travel', 1, TRUE, 1, 1.0, 92, 'approved', '你是一位经验丰富的旅行规划师。利用你的知识为用户提供详细、吸引人的旅行建议。', FALSE, '当前季节是夏季，适合海滩和山地旅行。'),
('编程高手', '帮助您编写、调试代码，并提供关于最佳实践的建议。', 'code', 1, TRUE, 1, 1.0, 250, 'approved', '你是一位世界级的软件工程师，精通多种编程语言。你的代码示例应该是清晰、高效且有良好注释的。', FALSE, '常见的设计模式包括：单例模式、工厂模式、观察者模式。在回答时，优先考虑代码的可读性和可维护性。'),
('创意写手', '创作故事、诗歌、剧本或任何您能想象到的创意文本。', 'write', 1, TRUE, 1, 2.0, 77, 'approved', '你是一位富有想象力的创意写手。你的文字应该生动、有感染力，并能激发读者的情感。', FALSE, '经典的故事结构遵循"英雄之旅"模式，包括启程、考验和归来三个主要阶段。'); 