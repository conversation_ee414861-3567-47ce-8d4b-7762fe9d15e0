version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ai-agent-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_agent_market
      MYSQL_USER: appuser
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./migrations:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # Go后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-agent-backend
    environment:
      - PORT=8080
      - ENVIRONMENT=production
      - DATABASE_URL=appuser:password@tcp(mysql:3306)/ai_agent_market?charset=utf8mb4&parseTime=True&loc=Local
      - JWT_SECRET=your-jwt-secret-key
      - GOOGLE_API_KEY=your-google-api-key
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    restart: unless-stopped
    volumes:
      - .:/app
    working_dir: /app

  # Vue3前端服务 (nginx)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: ai-agent-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  mysql_data: 