# 🌐 局域网访问配置指南

本指南将帮助您配置AI智能体市场平台，使其能够通过局域网IP访问。

## 📋 配置概述

项目已经配置为支持局域网访问，包括：
- ✅ 前端自动检测局域网IP
- ✅ 后端支持局域网CORS访问
- ✅ Vite开发服务器绑定到所有网络接口

## 🚀 快速启动

### 1. 获取本机IP地址

**macOS/Linux:**
```bash
# 查看所有网络接口
ifconfig | grep "inet " | grep -v 127.0.0.1

# 或者查看默认路由接口的IP
ipconfig getifaddr en0  # 通常是WiFi接口
```

**Windows:**
```bash
ipconfig | findstr IPv4
```

假设您的局域网IP是：`*************`

### 2. 启动后端服务

```bash
cd backend

# 设置环境变量
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=ai_user
export DB_PASSWORD=123456
export DB_NAME=ai_agent_market
export JWT_SECRET=your-secret-key
export PORT=8080

# 启动后端（默认监听所有接口）
go run cmd/main.go
```

### 3. 启动前端服务

```bash
cd frontend

# 安装依赖（如果还没有）
npm install

# 启动前端（自动绑定到所有网络接口）
npm run dev
```

## 🔗 访问地址

启动成功后，您可以通过以下地址访问：

- **本机访问**: http://localhost:3000
- **局域网访问**: http://*************:3000 (替换为您的实际IP)
- **手机访问**: http://*************:3000 (确保手机连接同一WiFi)

## ⚙️ 高级配置

### 自定义API地址

如果需要指定特定的后端地址，创建 `frontend/.env.local` 文件：

```bash
# frontend/.env.local
VITE_API_BASE_URL=http://*************:8080/api/v1
```

### 自定义CORS配置

如果需要限制CORS访问来源，设置环境变量：

```bash
# 只允许特定IP访问
export CORS_ALLOW_ORIGINS="http://*************:3000,http://*************:3000"
```

### 修改端口

**修改前端端口:**
编辑 `frontend/vite.config.ts`:
```typescript
server: {
  port: 8080, // 改为您想要的端口
  host: '0.0.0.0',
}
```

**修改后端端口:**
```bash
export PORT=9090  # 设置为您想要的端口
```

## 🔧 故障排除

### 问题1: 无法通过局域网IP访问前端

**解决方案:**
1. 确认Vite配置中 `host: '0.0.0.0'`
2. 检查防火墙是否阻挡端口3000
3. 确认设备在同一局域网

**macOS防火墙设置:**
```bash
# 临时关闭防火墙测试
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate off

# 重新开启防火墙
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate on
```

### 问题2: 前端无法连接后端API

**解决方案:**
1. 确认后端服务正在运行
2. 检查CORS配置是否正确
3. 验证API地址是否正确

**测试后端连接:**
```bash
# 测试后端健康状态
curl http://*************:8080/api/v1/public/agents
```

### 问题3: 手机无法访问

**解决方案:**
1. 确认手机与电脑连接同一WiFi
2. 检查路由器是否开启AP隔离
3. 确认IP地址正确

## 📱 移动设备访问

1. **确保设备连接同一WiFi网络**
2. **在手机浏览器输入**: http://YOUR_IP:3000
3. **添加到主屏幕**: 获得类似App的体验

## 🛡️ 安全注意事项

- ✅ 局域网访问相对安全，但仍需注意
- ✅ 不要在公共WiFi上开放局域网访问
- ✅ 生产环境请使用HTTPS和严格的CORS配置
- ✅ 定期更新依赖包和安全补丁

## 🔄 自动化脚本

创建启动脚本 `start-network.sh`:

```bash
#!/bin/bash

# 获取本机IP
IP=$(ipconfig getifaddr en0)
echo "本机IP地址: $IP"
echo "访问地址: http://$IP:3000"

# 启动后端
cd backend
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=ai_user
export DB_PASSWORD=123456
export DB_NAME=ai_agent_market
export JWT_SECRET=your-secret-key
export PORT=8080

go run cmd/main.go &
BACKEND_PID=$!

# 启动前端
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo "后端PID: $BACKEND_PID"
echo "前端PID: $FRONTEND_PID"
echo "按Ctrl+C停止服务"

# 等待中断信号
trap "kill $BACKEND_PID $FRONTEND_PID" EXIT
wait
```

**使用方法:**
```bash
chmod +x start-network.sh
./start-network.sh
```

## 📞 获取帮助

如果遇到问题：
1. 检查本文档的故障排除部分
2. 确认网络配置和防火墙设置
3. 查看控制台错误信息
4. 提交Issue到项目仓库

---

**🎉 配置完成后，您就可以在局域网内的任何设备上访问AI智能体市场平台了！** 