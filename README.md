# 🤖 AI智能体市场平台

> 一个现代化的AI智能体创建、分享和交易平台，让每个人都能轻松创建和获利于AI智能体

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org)
[![Vue Version](https://img.shields.io/badge/Vue-3.4+-green.svg)](https://vuejs.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

## 📖 项目概述

AI智能体市场平台是一个集创建、管理、分享和交易AI智能体于一体的综合性平台。用户可以：

- 🎯 **创建专属智能体**：基于不同AI模型定制个性化智能助手
- 💰 **智能体变现**：设置利润率，让您的创意直接产生收益
- 🛒 **市场交易**：浏览和购买其他用户创建的优质智能体
- 🤝 **联盟推广**：通过推广获得佣金，实现多元化收益
- 💬 **实时聊天**：与智能体进行自然流畅的对话交互
- 📊 **数据分析**：全面的使用统计和收益分析

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Go 1.24+ with Gin Web Framework
- **数据库**: MySQL 8.0+ with GORM ORM
- **认证**: JWT (JSON Web Tokens)
- **密码加密**: bcrypt
- **API**: RESTful API设计
- **容器化**: Docker & Docker Compose

### 前端技术栈
- **框架**: Vue 3.4+ with Composition API
- **构建工具**: Vite 5.0+
- **语言**: TypeScript 5.0+
- **状态管理**: Pinia
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios
- **样式**: 现代化CSS with 企业级设计

### 项目结构
```
ai-agent-market/
├── backend/                 # Go后端服务
│   ├── cmd/
│   │   └── main.go         # 应用入口
│   ├── internal/
│   │   ├── api/            # API路由和处理器
│   │   ├── config/         # 配置管理
│   │   ├── database/       # 数据库连接和迁移
│   │   ├── middleware/     # 中间件
│   │   ├── models/         # 数据模型
│   │   └── services/       # 业务逻辑服务
│   ├── migrations/         # 数据库迁移文件
│   ├── docker-compose.yml  # 开发环境编排
│   └── Dockerfile         # 容器化配置
├── frontend/               # Vue3前端应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── services/       # API服务
│   │   ├── types/          # TypeScript类型定义
│   │   └── router/         # 路由配置
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
└── README.md              # 项目文档
```

## 🚀 快速开始

### 环境要求
- **Go**: 1.24+
- **Node.js**: 18+
- **MySQL**: 8.0+
- **Git**: 最新版本

### 1. 克隆项目
```bash
git clone <repository-url>
cd ai-agent-market
```

### 2. 数据库设置
```bash
# 安装MySQL (macOS)
brew install mysql
brew services start mysql

# 创建数据库和用户
mysql -u root -p
```

```sql
CREATE DATABASE ai_agent_market;
CREATE USER 'ai_user'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON ai_agent_market.* TO 'ai_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 后端启动
```bash
cd backend

# 安装依赖
go mod tidy

# 设置环境变量
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=ai_user
export DB_PASSWORD=123456
export DB_NAME=ai_agent_market
export JWT_SECRET=your-secret-key
export PORT=8080

# 启动服务
go run cmd/main.go
```

### 4. 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问应用
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080

## 🔧 配置说明

### 环境变量
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 3306 |
| `DB_USER` | 数据库用户名 | ai_user |
| `DB_PASSWORD` | 数据库密码 | 123456 |
| `DB_NAME` | 数据库名称 | ai_agent_market |
| `JWT_SECRET` | JWT密钥 | your-secret-key |
| `PORT` | 服务端口 | 8080 |
| `DB_MAX_IDLE_CONNS` | 最大空闲连接数 | 10 |
| `DB_MAX_OPEN_CONNS` | 最大打开连接数 | 100 |
| `DB_CONN_MAX_LIFETIME` | 连接最大生命周期 | 1h |

## 🎯 核心功能

### 用户系统
- ✅ 用户注册/登录
- ✅ 个人资料管理
- ✅ 密码修改
- ✅ 角色权限管理（用户/管理员）

### 智能体管理
- ✅ 创建自定义智能体
- ✅ 智能体配置管理
- ✅ 角色设定和知识库
- ✅ 谷歌搜索集成
- ✅ 利润率设置

### 市场功能
- ✅ 智能体市场浏览
- ✅ 搜索和分类筛选
- ✅ 智能体详情展示
- ✅ 购买和使用

### 聊天系统
- ✅ 实时聊天界面
- ✅ 对话历史记录
- ✅ 多智能体切换
- ✅ 消息持久化

### 联盟推广
- ✅ 推广链接生成
- ✅ 佣金率设置
- ✅ 收益统计
- ✅ 推广数据分析

### 管理后台
- ✅ 用户管理
- ✅ 智能体审核
- ✅ 交易记录查看
- ✅ 系统统计

## 📚 API文档

### 认证相关
```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
GET  /api/v1/auth/profile     # 获取用户信息
PUT  /api/v1/auth/profile     # 更新用户信息
POST /api/v1/auth/change-password  # 修改密码
```

### 智能体管理
```
GET    /api/v1/agents/        # 获取智能体列表
POST   /api/v1/agents/        # 创建智能体
GET    /api/v1/agents/:id     # 获取智能体详情
PUT    /api/v1/agents/:id     # 更新智能体
DELETE /api/v1/agents/:id     # 删除智能体
```

### 公开接口
```
GET /api/v1/public/agents     # 获取公开智能体
GET /api/v1/public/models     # 获取AI模型列表
```

### 聊天功能
```
POST /api/v1/chat/conversations           # 创建对话
GET  /api/v1/chat/conversations/:id       # 获取对话详情
POST /api/v1/chat/conversations/:id/messages  # 发送消息
GET  /api/v1/chat/conversations/agent/:agentId  # 获取智能体对话列表
```

### 管理员接口
```
GET  /api/v1/admin/users          # 获取用户列表
PUT  /api/v1/admin/users/:id/ban  # 封禁用户
PUT  /api/v1/admin/users/:id/unban # 解封用户
GET  /api/v1/admin/agents         # 获取待审核智能体
PUT  /api/v1/admin/agents/:id/approve  # 审核通过
PUT  /api/v1/admin/agents/:id/reject   # 审核拒绝
GET  /api/v1/admin/transactions   # 获取交易记录
```

## 💾 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
id              BIGINT PRIMARY KEY AUTO_INCREMENT
email           VARCHAR(255) UNIQUE NOT NULL
password_hash   VARCHAR(255) NOT NULL
nickname        VARCHAR(100)
avatar_url      VARCHAR(500)
role            ENUM('user', 'admin') DEFAULT 'user'
is_banned       BOOLEAN DEFAULT FALSE
created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

#### 智能体表 (agents)
```sql
id                      BIGINT PRIMARY KEY AUTO_INCREMENT
user_id                 BIGINT NOT NULL
name                    VARCHAR(255) NOT NULL
description             TEXT
icon                    VARCHAR(500)
model_id                BIGINT NOT NULL
persona                 TEXT
knowledge               TEXT
use_google_search       BOOLEAN DEFAULT FALSE
is_public               BOOLEAN DEFAULT FALSE
status                  ENUM('draft', 'pending', 'approved', 'rejected') DEFAULT 'draft'
profit_margin           DECIMAL(5,2) DEFAULT 0.00
affiliate_enabled       BOOLEAN DEFAULT FALSE
affiliate_commission_rate DECIMAL(5,2) DEFAULT 0.00
created_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

#### 对话表 (conversations)
```sql
id         BIGINT PRIMARY KEY AUTO_INCREMENT
user_id    BIGINT NOT NULL
agent_id   BIGINT NOT NULL
title      VARCHAR(255)
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

#### 消息表 (messages)
```sql
id              BIGINT PRIMARY KEY AUTO_INCREMENT
conversation_id BIGINT NOT NULL
role            ENUM('user', 'assistant') NOT NULL
content         TEXT NOT NULL
created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

## 🔐 安全特性

- **JWT认证**: 安全的用户身份验证
- **密码加密**: bcrypt哈希加密存储
- **CORS配置**: 跨域请求安全控制
- **输入验证**: 前后端双重数据验证
- **SQL注入防护**: GORM ORM安全查询
- **XSS防护**: 前端输入过滤和转义

## 📱 用户界面特色

### 现代化设计
- 🎨 **企业级视觉设计**: 专业的渐变色彩和阴影效果
- 🌟 **毛玻璃效果**: 现代化的透明度和模糊效果
- 🎯 **响应式布局**: 完美适配桌面和移动设备
- ⚡ **流畅动画**: 微交互和过渡动画提升用户体验

### 用户体验优化
- 🔔 **全局消息通知**: 操作反馈和状态提示
- 📊 **加载状态指示**: 清晰的加载动画和进度提示
- 🎪 **空状态设计**: 友好的空数据页面引导
- 🔍 **智能搜索**: 实时搜索和筛选功能

## 🚢 部署指南

### Docker部署
```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境部署
1. **后端部署**
```bash
# 构建可执行文件
cd backend
go build -o bin/ai-agent-market cmd/main.go

# 使用systemd管理服务
sudo cp bin/ai-agent-market /usr/local/bin/
sudo cp scripts/ai-agent-market.service /etc/systemd/system/
sudo systemctl enable ai-agent-market
sudo systemctl start ai-agent-market
```

2. **前端部署**
```bash
# 构建生产版本
cd frontend
npm run build

# 使用nginx提供静态文件服务
sudo cp -r dist/* /var/www/html/
```

## 🧪 开发指南

### 后端开发
```bash
# 运行测试
go test ./...

# 代码格式化
go fmt ./...

# 静态分析
go vet ./...

# 生成模型
gorm gen
```

### 前端开发
```bash
# 类型检查
npm run type-check

# 代码格式化
npm run format

# 构建检查
npm run build-check

# 预览构建结果
npm run preview
```

### 代码规范
- **Go**: 遵循Go官方代码规范
- **Vue**: 使用Vue 3 Composition API最佳实践
- **TypeScript**: 严格类型检查，避免any类型
- **Git**: 使用语义化提交信息

## 👨‍💼 管理员指南

### 默认管理员账户
- **邮箱**: <EMAIL>
- **密码**: admin123

### 管理功能
1. **用户管理**: 查看用户列表，封禁/解封用户
2. **智能体审核**: 审核用户提交的公开智能体
3. **交易监控**: 查看平台交易记录和统计
4. **系统配置**: 管理AI模型和系统参数

## ❓ 常见问题

### Q: 数据库连接失败？
A: 检查MySQL服务是否启动，数据库配置是否正确。

### Q: 前端API请求404？
A: 确保后端服务正在运行在8080端口。

### Q: JWT token过期？
A: 重新登录获取新的token，考虑实现token自动刷新。

### Q: 智能体创建失败？
A: 检查所有必填字段是否填写，AI模型是否选择。

## 🤝 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。详情请查看 [LICENSE](LICENSE) 文件。

## 📞 联系我们

- **项目主页**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **技术讨论**: [GitHub Discussions]

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

<div align="center">

**🌟 如果这个项目对您有帮助，请给我们一个星标！ 🌟**

Made with ❤️ by AI Agent Market Team

</div>
